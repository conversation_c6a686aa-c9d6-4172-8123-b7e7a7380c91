import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

/// Manages notification permissions across Android and iOS platforms
class NotificationPermissionManager {
  static final NotificationPermissionManager _instance = 
      NotificationPermissionManager._internal();
  
  factory NotificationPermissionManager() => _instance;
  NotificationPermissionManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  /// Check if notification permissions are granted
  Future<bool> hasPermission() async {
    try {
      if (Platform.isAndroid) {
        return await _checkAndroidPermission();
      } else if (Platform.isIOS) {
        return await _checkIOSPermission();
      }
      return false;
    } catch (e) {
      Logger.error('Error checking notification permission: $e');
      return false;
    }
  }

  /// Request notification permissions with platform-specific handling
  Future<bool> requestPermission(BuildContext context) async {
    try {
      // Show explanation dialog first
      final shouldRequest = await _showPermissionExplanationDialog(context);
      if (!shouldRequest) return false;

      if (Platform.isAndroid) {
        return await _requestAndroidPermission();
      } else if (Platform.isIOS) {
        return await _requestIOSPermission();
      }
      return false;
    } catch (e) {
      Logger.error('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Check Android notification permission (API 33+)
  Future<bool> _checkAndroidPermission() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+), check POST_NOTIFICATIONS permission
      final status = await Permission.notification.status;
      return status.isGranted;
    }
    return true; // Assume granted for older Android versions
  }

  /// Check iOS notification permission
  Future<bool> _checkIOSPermission() async {
    final settings = await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    return settings ?? false;
  }

  /// Request Android notification permission
  Future<bool> _requestAndroidPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.request();
      return status.isGranted;
    }
    return true;
  }

  /// Request iOS notification permission
  Future<bool> _requestIOSPermission() async {
    final granted = await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    return granted ?? false;
  }

  /// Show permission explanation dialog
  Future<bool> _showPermissionExplanationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Enable Notifications'),
          content: const Text(
            'Lekky needs notification permission to alert you about:\n\n'
            '• Low balance warnings\n'
            '• Time to top-up reminders\n'
            '• Invalid record alerts\n\n'
            'These notifications help you manage your electricity usage effectively.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Allow'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Show settings dialog when permission is permanently denied
  Future<void> showSettingsDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
            'Notification permission is required for alerts. '
            'Please enable notifications in your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }
}
