import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Responsive dimensions for pagination layout
class _ResponsiveDimensions {
  final double buttonWidth;
  final double buttonHeight;
  final double spacing;
  final double iconButtonSize;
  final double fontSize;
  final double availableWidth;
  final bool showCompactLayout;

  const _ResponsiveDimensions({
    required this.buttonWidth,
    required this.buttonHeight,
    required this.spacing,
    required this.iconButtonSize,
    required this.fontSize,
    required this.availableWidth,
    required this.showCompactLayout,
  });
}

/// A widget that displays pagination controls for the History screen
class HistoryPagination extends StatelessWidget {
  /// The current page
  final int currentPage;

  /// The total number of pages
  final int totalPages;

  /// Callback when the previous page button is pressed
  final VoidCallback onPreviousPage;

  /// Callback when the next page button is pressed
  final VoidCallback onNextPage;

  /// Callback when a specific page is selected
  final ValueChanged<int> onPageSelected;

  /// Callback when the first page button is pressed
  final VoidCallback onFirstPage;

  /// Callback when the last page button is pressed
  final VoidCallback onLastPage;

  /// Constructor
  const HistoryPagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPreviousPage,
    required this.onNextPage,
    required this.onPageSelected,
    required this.onFirstPage,
    required this.onLastPage,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return _buildResponsivePagination(context, constraints);
        },
      ),
    );
  }

  /// Build responsive pagination layout based on available space
  Widget _buildResponsivePagination(
      BuildContext context, BoxConstraints constraints) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final orientation = mediaQuery.orientation;
    final availableWidth = constraints.maxWidth;

    // Calculate responsive dimensions
    final dimensions = _calculateResponsiveDimensions(
        screenWidth, orientation, availableWidth);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Page info with responsive sizing
        _buildPageInfo(context, dimensions),

        // Navigation controls with dynamic layout
        _buildNavigationControls(context, dimensions),
      ],
    );
  }

  /// Calculate responsive dimensions based on screen size and orientation
  _ResponsiveDimensions _calculateResponsiveDimensions(
      double screenWidth, Orientation orientation, double availableWidth) {
    // Screen size breakpoints
    final isSmallScreen = screenWidth < 360;
    final isMediumScreen = screenWidth >= 360 && screenWidth < 600;
    final isLargeScreen = screenWidth >= 600;

    // Orientation adjustments
    final isLandscape = orientation == Orientation.landscape;

    // Calculate button dimensions based on max page number
    final maxPageDigits = totalPages.toString().length;
    final baseButtonWidth = maxPageDigits == 1
        ? 28.0
        : maxPageDigits == 2
            ? 32.0
            : 36.0;

    // Responsive adjustments
    double buttonWidth = baseButtonWidth;
    double buttonHeight = 28.0;
    double spacing = 2.0;
    double iconButtonSize = 24.0;
    double fontSize = 14.0;

    if (isSmallScreen) {
      buttonWidth = baseButtonWidth * 0.9;
      buttonHeight = 26.0;
      spacing = 1.0;
      iconButtonSize = 22.0;
      fontSize = 12.0;
    } else if (isMediumScreen) {
      buttonWidth = baseButtonWidth;
      buttonHeight = 28.0;
      spacing = 2.0;
      iconButtonSize = 24.0;
      fontSize = 13.0;
    } else if (isLargeScreen) {
      buttonWidth = baseButtonWidth * 1.1;
      buttonHeight = 30.0;
      spacing = 3.0;
      iconButtonSize = 26.0;
      fontSize = 14.0;
    }

    // Landscape adjustments
    if (isLandscape && !isLargeScreen) {
      spacing *= 0.8;
      fontSize *= 0.95;
    }

    return _ResponsiveDimensions(
      buttonWidth: buttonWidth,
      buttonHeight: buttonHeight,
      spacing: spacing,
      iconButtonSize: iconButtonSize,
      fontSize: fontSize,
      availableWidth: availableWidth,
      showCompactLayout: isSmallScreen || (isLandscape && isMediumScreen),
    );
  }

  /// Build page info text with responsive sizing
  Widget _buildPageInfo(
      BuildContext context, _ResponsiveDimensions dimensions) {
    final theme = Theme.of(context);

    return Flexible(
      flex: 1,
      child: Text(
        'Page ${currentPage + 1} of $totalPages',
        style: TextStyle(
          fontSize: dimensions.fontSize,
          color: theme.colorScheme.onSurface,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Build navigation controls with dynamic layout
  Widget _buildNavigationControls(
      BuildContext context, _ResponsiveDimensions dimensions) {
    // Calculate required width for all controls
    final controlsWidth = _calculateControlsWidth(dimensions);
    final availableControlsWidth =
        dimensions.availableWidth * 0.7; // Reserve 30% for page info

    // Determine which controls to show based on available space
    final showFirstLast = !dimensions.showCompactLayout &&
        totalPages > 3 &&
        controlsWidth <= availableControlsWidth;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // First page button
        if (showFirstLast)
          _buildIconButton(
            context,
            Icons.first_page,
            currentPage > 0 ? () => _handleNavigation(onFirstPage) : null,
            'First page',
            dimensions,
          ),

        // Previous page button
        _buildIconButton(
          context,
          Icons.arrow_back,
          currentPage > 0 ? () => _handleNavigation(onPreviousPage) : null,
          'Previous page',
          dimensions,
        ),

        // Page buttons
        ..._buildPageButtons(context, dimensions),

        // Next page button
        _buildIconButton(
          context,
          Icons.arrow_forward,
          currentPage < totalPages - 1
              ? () => _handleNavigation(onNextPage)
              : null,
          'Next page',
          dimensions,
        ),

        // Last page button
        if (showFirstLast)
          _buildIconButton(
            context,
            Icons.last_page,
            currentPage < totalPages - 1
                ? () => _handleNavigation(onLastPage)
                : null,
            'Last page',
            dimensions,
          ),
      ],
    );
  }

  /// Calculate total width required for navigation controls
  double _calculateControlsWidth(_ResponsiveDimensions dimensions) {
    const iconButtonWidth = 48.0; // Standard IconButton width
    final pageButtonsCount = totalPages > 3 ? 3 : totalPages;
    const ellipsisWidth = 24.0; // Approximate ellipsis width

    double totalWidth = 0.0;

    // Previous/Next buttons (always shown)
    totalWidth += iconButtonWidth * 2;

    // First/Last buttons (conditional)
    if (totalPages > 3) {
      totalWidth += iconButtonWidth * 2;
    }

    // Page buttons
    totalWidth +=
        (dimensions.buttonWidth + dimensions.spacing * 2) * pageButtonsCount;

    // Ellipsis (only if needed)
    if (totalPages > 3) {
      totalWidth += ellipsisWidth;
    }

    return totalWidth;
  }

  /// Handle navigation with haptic feedback
  void _handleNavigation(VoidCallback callback) {
    HapticFeedback.lightImpact();
    callback();
  }

  /// Build icon button with responsive sizing
  Widget _buildIconButton(
    BuildContext context,
    IconData icon,
    VoidCallback? onPressed,
    String tooltip,
    _ResponsiveDimensions dimensions,
  ) {
    return IconButton(
      icon: Icon(icon, size: dimensions.iconButtonSize),
      onPressed: onPressed,
      tooltip: tooltip,
      constraints: const BoxConstraints(
        minWidth: 44.0, // Accessibility minimum
        minHeight: 44.0,
      ),
    );
  }

  /// Build the page buttons with responsive sizing
  List<Widget> _buildPageButtons(
      BuildContext context, _ResponsiveDimensions dimensions) {
    final theme = Theme.of(context);
    final List<Widget> pageButtons = [];

    // Determine which page buttons to show (maximum 3)
    const int maxVisiblePages = 3;
    int startPage = 0;
    int endPage = totalPages - 1;

    if (totalPages > maxVisiblePages) {
      // Show 3 pages centered around current page
      startPage = currentPage - 1;
      endPage = currentPage + 1;

      // Adjust if out of bounds
      if (startPage < 0) {
        endPage += -startPage;
        startPage = 0;
      }

      if (endPage >= totalPages) {
        startPage -= (endPage - totalPages + 1);
        endPage = totalPages - 1;
      }

      // Ensure startPage is not negative
      startPage = startPage < 0 ? 0 : startPage;
    }

    // Add ellipsis before if there's a gap at the start
    if (startPage > 0) {
      pageButtons.add(
        Container(
          padding: EdgeInsets.symmetric(horizontal: dimensions.spacing * 2),
          child: Text(
            '...',
            style: TextStyle(
              color: theme.colorScheme.onSurface,
              fontSize: dimensions.fontSize,
            ),
          ),
        ),
      );
    }

    // Add visible page buttons
    for (int i = startPage; i <= endPage; i++) {
      pageButtons.add(
        _buildPageButton(context, i, dimensions),
      );
    }

    // Add ellipsis after if there's a gap at the end
    if (endPage < totalPages - 1) {
      pageButtons.add(
        Container(
          padding: EdgeInsets.symmetric(horizontal: dimensions.spacing * 2),
          child: Text(
            '...',
            style: TextStyle(
              color: theme.colorScheme.onSurface,
              fontSize: dimensions.fontSize,
            ),
          ),
        ),
      );
    }

    return pageButtons;
  }

  /// Build a single page button with responsive sizing
  Widget _buildPageButton(
      BuildContext context, int page, _ResponsiveDimensions dimensions) {
    final theme = Theme.of(context);
    final bool isCurrentPage = page == currentPage;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: dimensions.spacing),
      child: InkWell(
        onTap: isCurrentPage ? null : () => _handlePageSelection(page),
        borderRadius: BorderRadius.circular(4),
        child: Container(
          width: dimensions.buttonWidth,
          height: dimensions.buttonHeight,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color:
                isCurrentPage ? theme.colorScheme.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: isCurrentPage
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withOpacity(0.5),
            ),
          ),
          child: Text(
            '${page + 1}',
            style: TextStyle(
              color: isCurrentPage
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface,
              fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
              fontSize: dimensions.fontSize,
            ),
          ),
        ),
      ),
    );
  }

  /// Handle page selection with haptic feedback
  void _handlePageSelection(int page) {
    HapticFeedback.lightImpact();
    onPageSelected(page);
  }
}
