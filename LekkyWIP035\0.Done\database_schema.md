# Database Schema for NewLekky App

This document outlines the database schema for the NewLekky app, including table structures, relationships, and query patterns.

## Overview

The NewLekky app uses SQLite with the sqflite package for local data storage. The database is designed to efficiently handle up to 630+ meter readings and top-ups while maintaining optimal query performance.

## Tables

### meter_readings

Stores individual meter readings entered by the user.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY | Unique identifier |
| value | REAL | NOT NULL | Meter reading value |
| date | TEXT | NOT NULL | Date and time in ISO 8601 format |
| is_valid | INTEGER | NOT NULL | Boolean flag (0 or 1) indicating validity |
| notes | TEXT | | Optional user notes |

**Indexes:**
- `date_index`: Index on the `date` column for efficient time-based queries

### top_ups

Stores top-up records entered by the user.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY | Unique identifier |
| amount | REAL | NOT NULL | Top-up amount |
| date | TEXT | NOT NULL | Date and time in ISO 8601 format |
| notes | TEXT | | Optional user notes |

**Indexes:**
- `date_index`: Index on the `date` column for efficient time-based queries

### averages

Stores precomputed averages to improve performance.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY | Unique identifier |
| recent_average | REAL | | Recent average daily usage |
| total_average | REAL | | Total average daily usage |
| last_updated | TEXT | NOT NULL | Date and time of last update in ISO 8601 format |

### settings

Stores application settings and preferences.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| key | TEXT | PRIMARY KEY | Setting identifier |
| value | TEXT | NOT NULL | Setting value |

## Relationships

Relationships between tables are primarily managed using date fields rather than foreign keys:

- **meter_readings and top_ups**: Related by date for time-based analysis
- **averages**: Updated when new data is added to meter_readings or top_ups

This approach provides flexibility while maintaining data integrity through application-level validation.

## Common Queries

### Read Operations

```sql
-- Get latest meter reading
SELECT * FROM meter_readings ORDER BY date DESC LIMIT 1;

-- Get entries within date range
SELECT * FROM meter_readings WHERE date BETWEEN ? AND ? ORDER BY date;
SELECT * FROM top_ups WHERE date BETWEEN ? AND ? ORDER BY date;

-- Get entries by type and validity
SELECT * FROM meter_readings WHERE is_valid = 1 ORDER BY date;

-- Get calculated averages
SELECT * FROM averages ORDER BY last_updated DESC LIMIT 1;
```

### Write Operations

```sql
-- Add new meter reading
INSERT INTO meter_readings (value, date, is_valid, notes) VALUES (?, ?, ?, ?);

-- Add new top-up
INSERT INTO top_ups (amount, date, notes) VALUES (?, ?, ?);

-- Update existing entry
UPDATE meter_readings SET value = ?, date = ?, is_valid = ?, notes = ? WHERE id = ?;
UPDATE top_ups SET amount = ?, date = ?, notes = ? WHERE id = ?;

-- Delete entry
DELETE FROM meter_readings WHERE id = ?;
DELETE FROM top_ups WHERE id = ?;

-- Update averages
UPDATE averages SET recent_average = ?, total_average = ?, last_updated = ? WHERE id = 1;
```

## Migration Strategy

The database includes a version-based migration system to handle schema changes:

1. Each database version has a corresponding migration function
2. Migrations run sequentially to bring the database to the latest version
3. Migrations preserve existing data when possible

Example migration pattern:

```dart
Future<void> _migrateV1ToV2(Database db) async {
  // Add new column to existing table
  await db.execute('ALTER TABLE meter_readings ADD COLUMN notes TEXT');
}

Future<void> _migrateV2ToV3(Database db) async {
  // Create new table
  await db.execute('''
    CREATE TABLE averages (
      id INTEGER PRIMARY KEY,
      recent_average REAL,
      total_average REAL,
      last_updated TEXT NOT NULL
    )
  ''');
  
  // Initialize with default data
  await db.insert('averages', {
    'id': 1,
    'recent_average': null,
    'total_average': null,
    'last_updated': DateTime.now().toIso8601String()
  });
}
```

## Performance Considerations

1. **Indexing**: Key columns are indexed for efficient queries
2. **Query Optimization**: Specific queries used instead of loading all data
3. **Batch Operations**: Related operations grouped in transactions
4. **Pagination**: Results paginated for large datasets (20-50 items per page)
5. **Caching**: Frequently accessed data cached in memory

## Data Integrity

1. **Transactions**: Critical operations wrapped in transactions
2. **Validation**: Data validated before insertion/update
3. **Constraints**: Database-level constraints for basic validation
4. **Backup**: Regular database backup functionality
5. **Recovery**: Ability to restore from backup

## Implementation Notes

- Use repository pattern to abstract database operations
- Implement data access through providers
- Cache query results to minimize database access
- Use transactions for related operations
- Implement periodic database optimization
