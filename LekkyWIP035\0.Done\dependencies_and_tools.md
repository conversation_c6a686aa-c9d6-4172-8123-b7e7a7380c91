# Dependencies and Tools for NewLekky App

This document outlines the dependencies, tools, and development environment required for the NewLekky app implementation.

## Development Environment

### Required Tools

| Tool | Version | Purpose |
|------|---------|---------|
| **Flutter SDK** | Latest stable (≥2.10.0) | Framework for building the app |
| **Dart SDK** | Latest stable (≥2.16.0) | Programming language |
| **Android Studio** | Latest version | IDE for Android development |
| **Xcode** | Latest version | IDE for iOS development (macOS only) |
| **VS Code** | Latest version | Alternative IDE with Flutter extensions |
| **Git** | Latest version | Version control |

### Extensions and Plugins

| Extension | Purpose |
|-----------|---------|
| **Flutter** | Flutter support for IDE |
| **Dart** | Dart language support |
| **Flutter Riverpod Snippets** | Code snippets for Riverpod |
| **Flutter Intl** | Internationalization support |
| **Bloc** | Support for BLoC pattern (optional) |

### CI/CD Tools

| Tool | Purpose |
|------|---------|
| **GitHub Actions** | Continuous integration and delivery |
| **Codemagic** | Flutter-specific CI/CD (alternative) |
| **Fastlane** | Automated building and releasing |

## Dependencies

### Core Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter** | Latest stable | Flutter framework |
| **flutter_localizations** | Same as Flutter | Localization support |
| **intl** | ^0.17.0 | Internationalization support |

### State Management

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_riverpod** | ^2.0.0 | State management |
| **riverpod_annotation** | ^1.0.0 | Code generation for Riverpod |
| **state_notifier** | ^0.7.0 | State container for immutable state |

### Navigation

| Package | Version | Purpose |
|---------|---------|---------|
| **go_router** | ^5.0.0 | Routing and navigation |
| **auto_route** | ^5.0.0 | Alternative routing solution (optional) |

### Database and Storage

| Package | Version | Purpose |
|---------|---------|---------|
| **sqflite** | ^2.0.0 | SQLite database |
| **path_provider** | ^2.0.0 | File system access |
| **shared_preferences** | ^2.0.0 | Key-value storage |
| **hive** | ^2.0.0 | Alternative NoSQL database (optional) |

### Dependency Injection

| Package | Version | Purpose |
|---------|---------|---------|
| **get_it** | ^7.0.0 | Service locator for dependency injection |
| **injectable** | ^1.0.0 | Code generation for dependency injection |

### UI Components

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_svg** | ^1.0.0 | SVG rendering |
| **cached_network_image** | ^3.0.0 | Image caching |
| **flutter_hooks** | ^0.18.0 | Reusable stateful logic |
| **google_fonts** | ^3.0.0 | Google Fonts integration |

### Data Visualization

| Package | Version | Purpose |
|---------|---------|---------|
| **fl_chart** | ^0.55.0 | Charts and graphs |
| **syncfusion_flutter_charts** | ^20.0.0 | Alternative charting library (optional) |

### Notifications

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_local_notifications** | ^12.0.0 | Local notifications |
| **awesome_notifications** | ^0.7.0 | Enhanced notifications (optional) |

### Utilities

| Package | Version | Purpose |
|---------|---------|---------|
| **uuid** | ^3.0.0 | UUID generation |
| **equatable** | ^2.0.0 | Equality comparisons |
| **collection** | ^1.16.0 | Collection utilities |
| **logger** | ^1.1.0 | Logging |
| **connectivity_plus** | ^3.0.0 | Network connectivity |

### Testing

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_test** | Same as Flutter | Widget testing |
| **mockito** | ^5.0.0 | Mocking for tests |
| **mocktail** | ^0.3.0 | Alternative mocking library |
| **flutter_driver** | Same as Flutter | Integration testing |
| **integration_test** | Same as Flutter | Integration testing |

### Code Generation

| Package | Version | Purpose |
|---------|---------|---------|
| **build_runner** | ^2.0.0 | Code generation |
| **json_serializable** | ^6.0.0 | JSON serialization |
| **freezed** | ^2.0.0 | Immutable classes |
| **riverpod_generator** | ^1.0.0 | Riverpod code generation |
| **injectable_generator** | ^1.0.0 | Injectable code generation |

### Linting and Formatting

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_lints** | ^2.0.0 | Lint rules |
| **dart_code_metrics** | ^4.0.0 | Additional static analysis |
| **very_good_analysis** | ^3.0.0 | Stricter lint rules (optional) |

## Development Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| **flutter_launcher_icons** | ^0.10.0 | App icon generation |
| **flutter_native_splash** | ^2.0.0 | Splash screen generation |
| **dart_code_metrics** | ^4.0.0 | Code quality metrics |
| **flutter_gen** | ^4.0.0 | Asset code generation |

## Version Control

### Branching Strategy

- **main**: Production-ready code
- **develop**: Development branch
- **feature/[feature-name]**: Feature branches
- **bugfix/[bug-name]**: Bug fix branches
- **release/[version]**: Release branches

### Commit Conventions

Follow the Conventional Commits specification:
- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation changes
- **style**: Changes that do not affect the meaning of the code
- **refactor**: Code changes that neither fix a bug nor add a feature
- **perf**: Performance improvements
- **test**: Adding or correcting tests
- **chore**: Changes to the build process or auxiliary tools

## Code Style

### Dart Style Guide

- Follow the official [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `dartfmt` for formatting
- Follow the linting rules defined in `analysis_options.yaml`

### Project Structure

```
lib/
├── core/                  # Core functionality
├── data/                  # Data layer
├── domain/                # Domain layer
├── features/              # Feature modules
├── di/                    # Dependency injection
└── main.dart              # App entry point
```

## Configuration Files

### pubspec.yaml

```yaml
name: new_lekky
description: A prepaid electricity meter tracking app
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=2.16.0 <3.0.0"
  flutter: ">=2.10.0"

dependencies:
  # Core
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.17.0

  # State Management
  flutter_riverpod: ^2.0.0
  riverpod_annotation: ^1.0.0

  # Navigation
  go_router: ^5.0.0

  # Database
  sqflite: ^2.0.0
  path_provider: ^2.0.0
  shared_preferences: ^2.0.0

  # Dependency Injection
  get_it: ^7.0.0
  injectable: ^1.0.0

  # UI
  flutter_svg: ^1.0.0
  google_fonts: ^3.0.0

  # Data Visualization
  fl_chart: ^0.55.0

  # Notifications
  flutter_local_notifications: ^12.0.0

  # Utilities
  uuid: ^3.0.0
  equatable: ^2.0.0
  logger: ^1.1.0

dev_dependencies:
  # Testing
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0
  integration_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.0.0
  json_serializable: ^6.0.0
  riverpod_generator: ^1.0.0
  injectable_generator: ^1.0.0

  # Linting
  flutter_lints: ^2.0.0
  dart_code_metrics: ^4.0.0

  # Development Tools
  flutter_launcher_icons: ^0.10.0
  flutter_native_splash: ^2.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
```

### analysis_options.yaml

```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  errors:
    invalid_annotation_target: ignore

linter:
  rules:
    - always_declare_return_types
    - avoid_empty_else
    - avoid_print
    - avoid_relative_lib_imports
    - avoid_returning_null_for_future
    - avoid_slow_async_io
    - avoid_type_to_string
    - avoid_types_as_parameter_names
    - avoid_web_libraries_in_flutter
    - cancel_subscriptions
    - close_sinks
    - comment_references
    - control_flow_in_finally
    - empty_statements
    - hash_and_equals
    - invariant_booleans
    - iterable_contains_unrelated_type
    - list_remove_unrelated_type
    - literal_only_boolean_expressions
    - no_adjacent_strings_in_list
    - no_duplicate_case_values
    - no_logic_in_create_state
    - prefer_const_constructors
    - prefer_const_declarations
    - prefer_final_fields
    - prefer_final_locals
    - prefer_void_to_null
    - unnecessary_statements
    - unrelated_type_equality_checks
    - use_key_in_widget_constructors
```

## Next Steps

1. Set up the development environment with the required tools
2. Create the Flutter project with the initial structure
3. Configure the dependencies in pubspec.yaml
4. Set up linting and formatting rules
5. Implement the core architecture components
