import 'package:sqflite/sqflite.dart';
import '../../../../core/constants/database_constants.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/bulk_operation_context.dart';
import '../../../../core/di/service_locator.dart';
import '../../../averages/domain/services/average_service.dart';
import '../../../validation/domain/services/validation_trigger_service.dart';
import '../../domain/models/top_up.dart';
import '../../domain/repositories/top_up_repository.dart';

/// Implementation of the top-up repository
class TopUpRepositoryImpl implements TopUpRepository {
  final DatabaseHelper _databaseHelper;

  /// Constructor
  TopUpRepositoryImpl(this._databaseHelper);

  @override
  Future<List<TopUp>> getAllTopUps() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date DESC',
      );

      return List.generate(maps.length, (i) {
        return TopUp.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get all top-ups: $e');
      return [];
    }
  }

  @override
  Future<List<TopUp>> getTopUps({
    required int page,
    required int pageSize,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date DESC',
        limit: pageSize,
        offset: page * pageSize,
      );

      return List.generate(maps.length, (i) {
        return TopUp.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get top-ups with pagination: $e');
      return [];
    }
  }

  @override
  Future<TopUp?> getTopUpById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.topUpsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return TopUp.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get top-up by ID: $e');
      return null;
    }
  }

  @override
  Future<List<TopUp>> getTopUpsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.topUpsTable,
        where: 'date BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'date DESC',
      );

      return List.generate(maps.length, (i) {
        return TopUp.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get top-ups by date range: $e');
      return [];
    }
  }

  @override
  Future<TopUp?> getLatestTopUp() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date DESC',
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return TopUp.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get latest top-up: $e');
      return null;
    }
  }

  @override
  Future<int> addTopUp(TopUp topUp) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.insert(
        DatabaseConstants.topUpsTable,
        topUp.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Update averages and fire event after successful insert
      if (result > 0) {
        BulkOperationContext.incrementOperationCount();

        // Only fire events and update averages if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          _triggerValidationAsync(result);
          EventBus().fire(EventType.dataUpdated);
          Logger.info(
              'TopUpRepository: Added top-up and fired dataUpdated event');
        } else {
          Logger.info('TopUpRepository: Added top-up (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to add top-up: $e');
      return -1;
    }
  }

  @override
  Future<int> updateTopUp(TopUp topUp) async {
    try {
      final db = await _databaseHelper.database;

      // Create a copy with updated timestamp
      final updatedTopUp = topUp.copyWith(
        updatedAt: DateTime.now(),
      );

      final result = await db.update(
        DatabaseConstants.topUpsTable,
        updatedTopUp.toMap(),
        where: 'id = ?',
        whereArgs: [topUp.id],
      );

      // Update averages and fire event after successful update
      if (result > 0) {
        BulkOperationContext.incrementOperationCount();

        // Only fire events and update averages if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          _triggerValidationUpdateAsync(topUp.id!);
          EventBus().fire(EventType.dataUpdated);
          Logger.info(
              'TopUpRepository: Updated top-up and fired dataUpdated event');
        } else {
          Logger.info('TopUpRepository: Updated top-up (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to update top-up: $e');
      return -1;
    }
  }

  @override
  Future<int> deleteTopUp(int id) async {
    try {
      // Get the top-up date before deletion for validation trigger
      final topUp = await getTopUpById(id);
      final topUpDate = topUp?.date;

      final db = await _databaseHelper.database;
      final result = await db.delete(
        DatabaseConstants.topUpsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // Update averages and fire event after successful delete
      if (result > 0) {
        BulkOperationContext.incrementOperationCount();

        // Only fire events and update averages if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          if (topUpDate != null) {
            _triggerValidationDeleteAsync(topUpDate);
          }
          EventBus().fire(EventType.dataUpdated);
          Logger.info(
              'TopUpRepository: Deleted top-up and fired dataUpdated event');
        } else {
          Logger.info('TopUpRepository: Deleted top-up (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to delete top-up: $e');
      return -1;
    }
  }

  @override
  Future<int> getTopUpsCount() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable}',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      Logger.error('Failed to get top-ups count: $e');
      return 0;
    }
  }

  @override
  Future<double> getTotalTopUpAmount({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        'SELECT SUM(amount) FROM ${DatabaseConstants.topUpsTable} WHERE date BETWEEN ? AND ?',
        [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
      );

      final sum = result.first['SUM(amount)'];
      if (sum == null) {
        return 0.0;
      }

      return sum is int ? sum.toDouble() : sum as double;
    } catch (e) {
      Logger.error('Failed to get total top-up amount: $e');
      return 0.0;
    }
  }

  /// Update averages asynchronously without blocking the main operation
  void _updateAveragesAsync() {
    // Skip if we're in a bulk operation to avoid excessive calculations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final averageService = serviceLocator<AverageService>();
      // Fire and forget - don't await to avoid blocking
      // AverageService will fire EventType.dataUpdated when calculation completes
      averageService.updateAverages().catchError((error) {
        Logger.error('Failed to update averages: $error');
      });
    } catch (e) {
      Logger.error('Failed to get AverageService: $e');
    }
  }

  /// Trigger validation asynchronously after adding a top-up
  void _triggerValidationAsync(int topUpId) {
    // Skip if we're in a bulk operation to avoid excessive validation
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final validationService = serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationService.validateAfterTopUpAdd(topUpId).catchError((error) {
        Logger.error('Failed to trigger validation after top-up add: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }

  /// Trigger validation asynchronously after updating a top-up
  void _triggerValidationUpdateAsync(int topUpId) {
    // Skip if we're in a bulk operation to avoid excessive validation
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final validationService = serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationService.validateAfterTopUpUpdate(topUpId).catchError((error) {
        Logger.error(
            'Failed to trigger validation after top-up update: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }

  /// Trigger validation asynchronously after deleting a top-up
  void _triggerValidationDeleteAsync(DateTime deletedTopUpDate) {
    // Skip if we're in a bulk operation to avoid excessive validation
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final validationService = serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationService
          .validateAfterTopUpDelete(deletedTopUpDate)
          .catchError((error) {
        Logger.error(
            'Failed to trigger validation after top-up delete: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }
}
