// File: lib/features/validation/presentation/screens/validation_dashboard_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../domain/models/integrity_report.dart';
import '../../domain/models/validation_issue.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../widgets/issue_card.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../entries/presentation/dialogs/edit_entry_dialog.dart';
import '../../../../core/services/preference_service.dart';
import '../../../../core/utils/date_formatter.dart';

/// The Validation Dashboard screen
class ValidationDashboardScreen extends StatefulWidget {
  /// Constructor
  const ValidationDashboardScreen({super.key});

  @override
  _ValidationDashboardScreenState createState() =>
      _ValidationDashboardScreenState();
}

class _ValidationDashboardScreenState extends State<ValidationDashboardScreen> {
  late ValidationDashboardController _controller;

  @override
  void initState() {
    super.initState();
    _controller = serviceLocator<ValidationDashboardController>();
    _loadData();
  }

  /// Load data from the controller
  Future<void> _loadData() async {
    await _controller.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Consumer<ValidationDashboardController>(
        builder: (context, controller, _) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Data Validation'),
              backgroundColor: AppColors.primary,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.of(context).pop(),
                tooltip: 'Back to History',
              ),
              actions: _buildAppBarActions(controller),
            ),
            body: _buildBody(controller),
          );
        },
      ),
    );
  }

  /// Build the app bar actions
  List<Widget> _buildAppBarActions(ValidationDashboardController controller) {
    return [
      _buildFilterIcon(controller),
      IconButton(
        icon: const Icon(Icons.refresh),
        tooltip: 'Refresh',
        onPressed: controller.refresh,
      ),
    ];
  }

  /// Build the filter icon with dynamic styling and animation
  Widget _buildFilterIcon(ValidationDashboardController controller) {
    final filterColor = _getFilterIconColor(controller.filterType);
    final isFilterActive =
        controller.filterType != ValidationIssueFilterType.all;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.all(8),
      decoration: isFilterActive
          ? BoxDecoration(
              color: filterColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: AnimatedScale(
        scale: isFilterActive ? 1.1 : 1.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: IconButton(
          icon: Icon(
            Icons.filter_list,
            color: isFilterActive ? filterColor : Colors.white,
          ),
          tooltip: 'Filter',
          onPressed: () => _showFilterDialog(controller),
        ),
      ),
    );
  }

  /// Get filter icon color based on filter type
  Color _getFilterIconColor(ValidationIssueFilterType filterType) {
    switch (filterType) {
      case ValidationIssueFilterType.all:
        return Colors.white;
      case ValidationIssueFilterType.highSeverity:
        return Colors.red;
      case ValidationIssueFilterType.mediumSeverity:
        return Colors.orange;
      case ValidationIssueFilterType.lowSeverity:
        return Colors.blue;
    }
  }

  /// Build the body of the screen
  Widget _buildBody(ValidationDashboardController controller) {
    if (controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (controller.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              controller.errorMessage!,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            LekkyButton(
              text: 'Retry',
              type: LekkyButtonType.primary,
              onPressed: controller.refresh,
            ),
          ],
        ),
      );
    }

    if (controller.filteredIssues.isEmpty) {
      return _buildEmptyState(controller);
    }

    return Column(
      children: [
        // Summary card
        if (controller.integrityReport != null)
          _buildSummaryCard(controller.integrityReport!),

        // Issues list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: controller.filteredIssues.length,
            itemBuilder: (context, index) {
              final issue = controller.filteredIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: IssueCard(
                  issue: issue,
                  onFix: (issue) => _showEditEntry(controller, issue),
                  onIgnore: null,
                  onTap: (issue) => _showIssueDetails(controller, issue),
                  isSelected: false,
                  selectionMode: false,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Build the empty state
  Widget _buildEmptyState(ValidationDashboardController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle_outline,
            color: Colors.green,
            size: 64,
          ),
          const SizedBox(height: 16),
          const Text(
            'No validation issues found',
            style: AppTextStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'All your data is valid and consistent',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          LekkyButton(
            text: 'Refresh',
            type: LekkyButtonType.primary,
            onPressed: controller.refresh,
          ),
        ],
      ),
    );
  }

  /// Build the summary card
  Widget _buildSummaryCard(IntegrityReport report) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: AppCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Integrity Summary',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Entries checked
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Entries Checked:'),
                Text(
                  '${report.totalEntriesChecked}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Valid entries
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Valid Entries:'),
                Text(
                  '${report.validEntriesCount} (${report.validPercentage.toStringAsFixed(1)}%)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Invalid entries
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Invalid Entries:'),
                Text(
                  '${report.invalidEntriesCount} (${report.invalidPercentage.toStringAsFixed(1)}%)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Issues by severity
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('High Severity Issues:'),
                Text(
                  '${report.highSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Medium Severity Issues:'),
                Text(
                  '${report.mediumSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Low Severity Issues:'),
                Text(
                  '${report.lowSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Last check time
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Last Check:'),
                Text(
                  _formatDateTime(report.generatedAt),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  /// Show the filter dialog
  void _showFilterDialog(ValidationDashboardController controller) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Container(
          width: dialogWidth,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDialogHeader(context),
              const SizedBox(height: 24),
              _buildFilterSeverityDropdown(context, controller),
              const SizedBox(height: 32),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.tune,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Filter Issues',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build filter severity dropdown
  Widget _buildFilterSeverityDropdown(
      BuildContext context, ValidationDashboardController controller) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filter by Severity',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ValidationIssueFilterType>(
              value: controller.filterType,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: [
                ValidationIssueFilterType.all,
                ValidationIssueFilterType.highSeverity,
                ValidationIssueFilterType.mediumSeverity,
                ValidationIssueFilterType.lowSeverity,
              ].map((type) {
                String label;
                switch (type) {
                  case ValidationIssueFilterType.all:
                    label = 'All Issues';
                    break;
                  case ValidationIssueFilterType.highSeverity:
                    label = 'High Severity';
                    break;
                  case ValidationIssueFilterType.mediumSeverity:
                    label = 'Medium Severity';
                    break;
                  case ValidationIssueFilterType.lowSeverity:
                    label = 'Low Severity';
                    break;
                  default:
                    label = 'Unknown';
                    break;
                }

                return DropdownMenuItem<ValidationIssueFilterType>(
                  value: type,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.setFilterType(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
      ],
    );
  }

  /// Show edit entry dialog for an issue
  Future<void> _showEditEntry(
    ValidationDashboardController controller,
    ValidationIssue issue,
  ) async {
    Logger.info(
        'ValidationDashboard: Attempting to show edit entry for issue: entryId=${issue.entryId}, type=${issue.type}');

    // Get the entry associated with the issue
    final entry = await controller.getEntryForIssue(issue);

    if (entry == null) {
      Logger.warning(
          'ValidationDashboard: Entry not found for issue entryId: ${issue.entryId}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Entry not found')),
        );
      }
      return;
    }

    if (!mounted) return;

    // Get currency symbol from preferences
    final preferenceService = serviceLocator<PreferenceService>();
    final currencySymbol = preferenceService.currencySymbol;

    // Navigate to edit entry based on entry type
    if (entry is MeterReading) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: entry,
          topUp: null,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => controller.refresh(),
          onEntryDeleted: () => controller.refresh(),
        ),
      );
    } else if (entry is TopUp) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: null,
          topUp: entry,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => controller.refresh(),
          onEntryDeleted: () => controller.refresh(),
        ),
      );
    }
  }

  /// Show issue details
  Future<void> _showIssueDetails(
    ValidationDashboardController controller,
    ValidationIssue issue,
  ) async {
    // Get the entry associated with the issue
    final entry = await controller.getEntryForIssue(issue);

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text(
          'Issue Details',
          style: AppTextStyles.titleMedium,
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Issue type and severity
              Row(
                children: [
                  _buildIssueTypeIcon(issue.type),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getIssueTypeText(issue.type),
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildSeverityBadge(issue.severity),
                ],
              ),
              const Divider(),

              // Issue message
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  _formatIssueMessage(issue),
                  style: AppTextStyles.bodyMedium,
                ),
              ),

              // Entry details if available
              if (entry != null) _buildEntryDetails(entry),

              // Metadata if available
              if (issue.metadata != null && issue.metadata!.isNotEmpty)
                _buildMetadataDetails(issue.metadata!),

              // Detection date
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Detected: ${_formatDateTime(issue.detectedAt)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          LekkyButton(
            text: 'Close',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(dialogContext).pop(),
          ),
          const SizedBox(width: 8),
          LekkyButton(
            text: 'Edit Entry',
            type: LekkyButtonType.special,
            size: LekkyButtonSize.compact,
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _showEditEntry(controller, issue);
            },
          ),
        ],
      ),
    );
  }

  /// Build entry details
  Widget _buildEntryDetails(dynamic entry) {
    if (entry == null) {
      return const SizedBox.shrink();
    }

    if (entry is MeterReading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Meter Reading Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Value: ${entry.value}'),
          Text('Date: ${_formatDate(entry.date)}'),
          Text('Valid: ${entry.isValid ? 'Yes' : 'No'}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
        ],
      );
    } else if (entry is TopUp) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Top-up Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Amount: ${entry.amount}'),
          Text('Date: ${_formatDate(entry.date)}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
          if (entry.paymentMethod != null && entry.paymentMethod!.isNotEmpty)
            Text('Payment Method: ${entry.paymentMethod}'),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Build metadata details
  Widget _buildMetadataDetails(Map<String, dynamic> metadata) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          'Additional Information',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...metadata.entries.map((entry) {
          return Text('${_formatMetadataKey(entry.key)}: ${entry.value}');
        }),
      ],
    );
  }

  /// Format a metadata key for display
  String _formatMetadataKey(String key) {
    // Convert snake_case to Title Case
    return key
        .split('_')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  /// Build the issue type icon
  Widget _buildIssueTypeIcon(ValidationIssueType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case ValidationIssueType.negativeValue:
        iconData = Icons.remove_circle_outline;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.futureDate:
        iconData = Icons.event_busy;
        iconColor = Colors.orange;
        break;

      case ValidationIssueType.chronologicalOrder:
        iconData = Icons.swap_vert;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.balanceInconsistency:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.duplicateEntry:
        iconData = Icons.content_copy;
        iconColor = Colors.orange;
        break;

      case ValidationIssueType.missingEntry:
        iconData = Icons.calendar_today;
        iconColor = Colors.blue;
        break;

      case ValidationIssueType.other:
        iconData = Icons.error_outline;
        iconColor = Colors.red;
        break;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 24,
    );
  }

  /// Build the severity badge
  Widget _buildSeverityBadge(ValidationIssueSeverity severity) {
    Color badgeColor;
    String severityText;

    switch (severity) {
      case ValidationIssueSeverity.high:
        badgeColor = Colors.red;
        severityText = 'High';
        break;

      case ValidationIssueSeverity.medium:
        badgeColor = Colors.orange;
        severityText = 'Medium';
        break;

      case ValidationIssueSeverity.low:
        badgeColor = Colors.blue;
        severityText = 'Low';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        severityText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get the issue type text
  String _getIssueTypeText(ValidationIssueType type) {
    switch (type) {
      case ValidationIssueType.negativeValue:
        return 'Negative Value';

      case ValidationIssueType.futureDate:
        return 'Future Date';

      case ValidationIssueType.chronologicalOrder:
        return 'Chronological Order';

      case ValidationIssueType.balanceInconsistency:
        return 'Balance Inconsistency';

      case ValidationIssueType.duplicateEntry:
        return 'Duplicate Entry';

      case ValidationIssueType.missingEntry:
        return 'Missing Entry';

      case ValidationIssueType.other:
        return 'Other Issue';
    }
  }

  /// Format a date for display
  String _formatDate(DateTime date) {
    return DateFormatter.formatDateForValidation(date);
  }

  /// Format a date and time for display
  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Format issue message to show "expected less than" for balance inconsistency
  String _formatIssueMessage(ValidationIssue issue) {
    final preferenceService = serviceLocator<PreferenceService>();
    final currencySymbol = preferenceService.currencySymbol;

    if (issue.type == ValidationIssueType.balanceInconsistency &&
        issue.metadata != null &&
        issue.metadata!.containsKey('expected_value')) {
      final expectedValue = issue.metadata!['expected_value'] as double;

      // Include detected date and expected date in the message
      String message = 'Balance inconsistency detected: ';

      if (issue.metadata!.containsKey('detected_date')) {
        final detectedDate = issue.metadata!['detected_date'] as DateTime;
        message += '${DateFormatter.formatDateForValidation(detectedDate)}, ';
      }

      message +=
          'expected less than $currencySymbol${expectedValue.toStringAsFixed(2)}';

      if (issue.metadata!.containsKey('expected_date')) {
        final expectedDate = issue.metadata!['expected_date'] as DateTime;
        message += ' by ${DateFormatter.formatDateForValidation(expectedDate)}';
      }

      return message;
    }
    return issue.message;
  }
}
