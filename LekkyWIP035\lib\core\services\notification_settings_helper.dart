import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../utils/logger.dart';
import 'notification_permission_manager.dart';
import 'background_monitoring_service.dart';

/// Helper service for managing notification settings and permissions
class NotificationSettingsHelper {
  static final NotificationSettingsHelper _instance =
      NotificationSettingsHelper._internal();

  factory NotificationSettingsHelper() => _instance;
  NotificationSettingsHelper._internal();

  /// Check if this is the first time any notification type is being enabled
  Future<bool> isFirstNotificationActivation() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;

      // Check if any notification type is currently enabled
      final anyEnabled =
          lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;

      return !anyEnabled;
    } catch (e) {
      Logger.error('Error checking first notification activation: $e');
      return false;
    }
  }

  /// Handle notification type activation with permission request
  Future<bool> handleNotificationActivation(
    BuildContext context,
    String notificationType,
  ) async {
    try {
      // Check if this is the first notification being enabled
      final isFirst = await isFirstNotificationActivation();

      if (isFirst) {
        // Request permissions for first-time activation
        final permissionManager = NotificationPermissionManager();
        final hasPermission = await permissionManager.hasPermission();

        if (!hasPermission) {
          final granted = await permissionManager.requestPermission(context);
          if (!granted) {
            // Show settings dialog if permission denied
            await permissionManager.showSettingsDialog(context);
            return false;
          }
        }

        // Initialize background monitoring for first-time setup
        try {
          final backgroundService = BackgroundMonitoringService();
          await backgroundService.updateMonitoring();
        } catch (e) {
          Logger.error('Failed to initialize background monitoring: $e');
          // Continue even if background monitoring fails
        }
      }

      return true;
    } catch (e) {
      Logger.error('Error handling notification activation: $e');
      return false;
    }
  }

  /// Check if notification permissions are available
  Future<bool> hasNotificationPermissions() async {
    try {
      final permissionManager = NotificationPermissionManager();
      return await permissionManager.hasPermission();
    } catch (e) {
      Logger.error('Error checking notification permissions: $e');
      return false;
    }
  }

  /// Show permission explanation dialog
  Future<void> showPermissionExplanation(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permissions'),
          content: const Text(
            'To receive alerts about your electricity usage, Lekky needs '
            'permission to send notifications. This allows the app to:\n\n'
            '• Alert you when your balance is low\n'
            '• Remind you when it\'s time to top up\n'
            '• Notify you about data validation issues\n\n'
            'You can manage these settings at any time.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Update background monitoring based on current settings
  Future<void> updateBackgroundMonitoring() async {
    try {
      final backgroundService = BackgroundMonitoringService();
      await backgroundService.updateMonitoring();
    } catch (e) {
      Logger.error('Error updating background monitoring: $e');
    }
  }
}
