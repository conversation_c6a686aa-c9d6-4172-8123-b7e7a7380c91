import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/constants/database_constants.dart';
import '../../../../core/models/average.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/repositories/average_repository.dart';

/// Implementation of the average repository
class AverageRepositoryImpl implements AverageRepository {
  final DatabaseHelper _databaseHelper;

  /// Constructor
  AverageRepositoryImpl(this._databaseHelper);

  @override
  Future<Average?> getLatestAverages() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.averagesTable,
        orderBy: 'last_updated DESC',
        limit: 1,
      );
      
      if (maps.isNotEmpty) {
        return Average.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get latest averages: $e');
      return null;
    }
  }

  @override
  Future<int> saveAverages(Average average) async {
    try {
      final db = await _databaseHelper.database;
      
      // Check if we have existing averages
      final existing = await getLatestAverages();
      
      if (existing != null) {
        // Update existing record
        return await db.update(
          DatabaseConstants.averagesTable,
          average.toMap(),
          where: 'id = ?',
          whereArgs: [existing.id],
        );
      } else {
        // Insert new record
        return await db.insert(
          DatabaseConstants.averagesTable,
          average.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    } catch (e) {
      Logger.error('Failed to save averages: $e');
      return -1;
    }
  }

  @override
  Future<bool> hasAverages() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.averagesTable}',
      );
      final count = Sqflite.firstIntValue(result) ?? 0;
      return count > 0;
    } catch (e) {
      Logger.error('Failed to check if averages exist: $e');
      return false;
    }
  }

  @override
  Future<int> deleteAllAverages() async {
    try {
      final db = await _databaseHelper.database;
      return await db.delete(DatabaseConstants.averagesTable);
    } catch (e) {
      Logger.error('Failed to delete all averages: $e');
      return -1;
    }
  }
}
