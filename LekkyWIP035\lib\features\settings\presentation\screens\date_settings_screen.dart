import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';

/// Date settings screen
class DateSettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const DateSettingsScreen({super.key});

  @override
  ConsumerState<DateSettingsScreen> createState() => _DateSettingsScreenState();
}

class _DateSettingsScreenState extends ConsumerState<DateSettingsScreen> {
  late String _selectedDateFormat;
  late bool _showTimeWithDate;

  @override
  void initState() {
    super.initState();
    // Initialize with defaults, will be updated when provider loads
    _selectedDateFormat = 'dd-MM-yyyy';
    _showTimeWithDate = true;
  }

  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Date Settings'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading settings: $error'),
        ),
        data: (settings) {
          // Update local state when data loads
          if (_selectedDateFormat != settings.dateFormat ||
              _showTimeWithDate != settings.showTimeWithDate) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _selectedDateFormat = settings.dateFormat;
                _showTimeWithDate = settings.showTimeWithDate;
              });
            });
          }

          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Date Format section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Date Format',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: $_selectedDateFormat',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Choose how dates will be displayed throughout the app',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Date format options
                      _buildDateFormatOption(
                          'dd-MM-yyyy', 'DD-MM-YYYY', '31-12-2023'),
                      _buildDateFormatOption(
                          'MM-dd-yyyy', 'MM-DD-YYYY', '12-31-2023'),
                      _buildDateFormatOption(
                          'yyyy-MM-dd', 'YYYY-MM-DD', '2023-12-31'),
                    ],
                  ),
                ),
              ),

              // Date Info section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.access_time, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Date Info',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${_showTimeWithDate ? "Date and time" : "Date only"}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Choose whether to show time with date',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Date info options
                      _buildDateInfoOption(false, 'Date only', '31-12-2023'),
                      _buildDateInfoOption(
                          true, 'Date and time', '31-12-2023 14:30'),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDateFormatOption(
      String formatValue, String displayFormat, String example) {
    return RadioListTile<String>(
      title: Text(displayFormat),
      subtitle: Text('Example: $example'),
      value: formatValue,
      groupValue: _selectedDateFormat,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedDateFormat = value;
          });
          ref.read(settingsProvider.notifier).updateDateFormatFromString(value);
        }
      },
    );
  }

  Widget _buildDateInfoOption(bool showTime, String title, String example) {
    return RadioListTile<bool>(
      title: Text(title),
      subtitle: Text('Example: $example'),
      value: showTime,
      groupValue: _showTimeWithDate,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _showTimeWithDate = value;
          });
          ref.read(settingsProvider.notifier).updateShowTimeWithDate(value);
        }
      },
    );
  }
}
