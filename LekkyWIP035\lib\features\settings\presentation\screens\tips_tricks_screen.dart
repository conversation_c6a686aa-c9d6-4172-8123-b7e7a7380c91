import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import 'package:url_launcher/url_launcher.dart';

/// Tips & Tricks screen with Contact & Support
class TipsTricksScreen extends StatelessWidget {
  /// Constructor
  const TipsTricksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Banner with back arrow
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: AppBanner(
              message: '← Tips & Tricks',
              gradientColors: AppColors.getSettingsMainCardGradient(
                  Theme.of(context).brightness == Brightness.dark),
              textColor: AppColors.getAppBarTextColor(
                  'settings', Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tips & Tricks section
                  Card(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.lightbulb, color: Colors.amber),
                              SizedBox(width: 16),
                              Text(
                                'Tips & Tricks',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Tips list
                          _buildTipItem(
                            'Enter readings at consistent times for more accurate averages',
                            color: Colors.amber,
                          ),
                          _buildTipItem(
                            'Use the history screen to identify usage patterns',
                            color: Colors.amber,
                          ),
                          _buildTipItem(
                            'Set up notifications to stay on top of your usage',
                            color: Colors.amber,
                          ),
                          _buildTipItem(
                            'Regular top-ups help maintain a steady credit balance',
                            color: Colors.amber,
                          ),
                          _buildTipItem(
                            'Check the cost screen to monitor your spending',
                            color: Colors.amber,
                          ),

                          // Notification-specific tips
                          _buildNotificationTipSection(),

                          // Permissions section
                          _buildPermissionsTipSection(),

                          // Troubleshooting section
                          _buildTroubleshootingTipSection(),
                        ],
                      ),
                    ),
                  ),

                  // Contact & Support section
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.contact_support, color: Colors.blue),
                              SizedBox(width: 16),
                              Text(
                                'Contact & Support',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Contact info
                          const Text(
                            'If you have any questions, suggestions, or issues, please contact us. Please note that I am a sole developer with limited resources and this is a hobby project:',
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Email contact
                          _buildContactItem(
                            icon: Icons.email,
                            title: 'Email Support',
                            subtitle: '<EMAIL>',
                            onTap: () => _launchEmail('<EMAIL>'),
                          ),

                          const SizedBox(height: 12),

                          // GitHub contact
                          _buildContactItem(
                            icon: Icons.code,
                            title: 'GitHub Issues',
                            subtitle: 'Report bugs or request features',
                            onTap: () =>
                                _launchUrl('https://github.com/lekky/issues'),
                          ),

                          const SizedBox(height: 12),

                          // Website contact
                          _buildContactItem(
                            icon: Icons.web,
                            title: 'Website',
                            subtitle: 'www.lekky.app',
                            onTap: () => _launchUrl('https://www.lekky.app'),
                          ),

                          const SizedBox(height: 16),

                          // Support note
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.blue.withOpacity(0.3),
                              ),
                            ),
                            child: const Row(
                              children: [
                                Icon(Icons.info, color: Colors.blue, size: 20),
                                SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'We will attempt to respond to support requests ASAP.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.blue,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a tip item
  Widget _buildTipItem(String tip, {Color color = Colors.green}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// Build notification-specific tips section
  Widget _buildNotificationTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Notification tips header
        const Row(
          children: [
            Icon(Icons.notifications_active, size: 18, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Notification Tips',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Notification tips
        _buildNotificationTip(
          Icons.notifications,
          'Tap the notification bell icon on the Dashboard to view all notifications',
        ),
        _buildNotificationTip(
          Icons.settings,
          'Configure notification types in Settings → Alerts & Notifications',
        ),
        _buildNotificationTip(
          Icons.schedule,
          'Set up reminders to check your meter regularly',
        ),
        _buildNotificationTip(
          Icons.warning,
          'Enable low balance alerts to avoid running out of credit',
        ),
        _buildNotificationTip(
          Icons.build,
          'Use Utilities in Alerts & Notifications to manage permissions and reset notification data',
        ),
        _buildNotificationTip(
          Icons.bug_report,
          'Access Testing → Notification Debug for advanced troubleshooting and system status',
        ),
      ],
    );
  }

  /// Build a notification tip item
  Widget _buildNotificationTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build permissions tips section
  Widget _buildPermissionsTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Permissions tips header
        const Row(
          children: [
            Icon(Icons.security, size: 18, color: Colors.green),
            SizedBox(width: 8),
            Text(
              'Notification Permissions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Permission requirements
        _buildPermissionTip(
          Icons.android,
          'Android 13+ (API 33+): POST_NOTIFICATIONS permission must be granted for alerts to work',
        ),
        _buildPermissionTip(
          Icons.phone_iphone,
          'iOS: Alert, badge, and sound permissions must be granted for notifications',
        ),
        _buildPermissionTip(
          Icons.info_outline,
          'Older Android versions: Notification permissions are automatically granted',
        ),
        _buildPermissionTip(
          Icons.build,
          'Use "Request Permissions" in Settings → Alerts & Notifications → Utilities to easily grant permissions',
        ),
        _buildPermissionTip(
          Icons.lightbulb_outline,
          'Individual alert types (Low Balance, Threshold) work even if main notifications toggle is off',
        ),
        _buildPermissionTip(
          Icons.settings,
          'If notifications stop working, check your device notification settings for Lekky',
        ),
      ],
    );
  }

  /// Build troubleshooting tips section
  Widget _buildTroubleshootingTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Troubleshooting tips header
        const Row(
          children: [
            Icon(Icons.help_outline, size: 18, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              'Troubleshooting',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Troubleshooting tips
        _buildTroubleshootingTip(
          Icons.notifications_off,
          'Not receiving notifications? Check Settings → Alerts & Notifications → Utilities → Request Permissions',
        ),
        _buildTroubleshootingTip(
          Icons.refresh,
          'Notifications acting up? Use "Clear All Notification Data" in Utilities to reset the system',
        ),
        _buildTroubleshootingTip(
          Icons.bug_report,
          'For detailed diagnostics, go to Settings → Testing → Notification Debug → Generate Status Report',
        ),
        _buildTroubleshootingTip(
          Icons.toggle_on,
          'Individual alert types work independently - you can enable Low Balance alerts without the main toggle',
        ),
        _buildTroubleshootingTip(
          Icons.phone_android,
          'On Android 13+, ensure "Allow notifications" is enabled in your device settings for Lekky',
        ),
      ],
    );
  }

  /// Build a troubleshooting tip item
  Widget _buildTroubleshootingTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.purple,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a permission tip item
  Widget _buildPermissionTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a contact item
  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Icon(icon, color: Colors.blue),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  /// Launch email
  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Lekky App Support',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  /// Launch URL
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
