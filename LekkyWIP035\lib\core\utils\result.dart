// File: lib/core/utils/result.dart
import 'app_error.dart';

/// A class that represents the result of an operation that can either succeed with a value or fail with an error
class Result<T> {
  /// The value if the operation succeeded
  final T? _value;
  
  /// The error if the operation failed
  final AppError? _error;
  
  /// Whether the operation succeeded
  final bool _isSuccess;
  
  /// Private constructor for success
  const Result._success(this._value)
      : _error = null,
        _isSuccess = true;
  
  /// Private constructor for failure
  const Result._failure(this._error)
      : _value = null,
        _isSuccess = false;
  
  /// Create a success result with a value
  factory Result.success(T value) => Result._success(value);
  
  /// Create a failure result with an error
  factory Result.failure(AppError error) => Result._failure(error);
  
  /// Whether the operation succeeded
  bool get isSuccess => _isSuccess;
  
  /// Whether the operation failed
  bool get isFailure => !_isSuccess;
  
  /// Get the value (throws if the operation failed)
  T get value {
    if (isSuccess) {
      return _value as T;
    }
    throw _error!;
  }
  
  /// Get the error (throws if the operation succeeded)
  AppError get error {
    if (isFailure) {
      return _error!;
    }
    throw Exception('Cannot get error from a successful result');
  }
  
  /// Apply functions based on success or failure
  R fold<R>({
    required R Function(T) onSuccess,
    required R Function(AppError) onFailure,
  }) {
    if (isSuccess) {
      return onSuccess(_value as T);
    } else {
      return onFailure(_error!);
    }
  }
  
  /// Map the value if the operation succeeded
  Result<R> map<R>(R Function(T) mapper) {
    if (isSuccess) {
      return Result.success(mapper(_value as T));
    } else {
      return Result.failure(_error!);
    }
  }
  
  /// Flat map the value if the operation succeeded
  Result<R> flatMap<R>(Result<R> Function(T) mapper) {
    if (isSuccess) {
      return mapper(_value as T);
    } else {
      return Result.failure(_error!);
    }
  }
  
  @override
  String toString() {
    if (isSuccess) {
      return 'Success: $_value';
    } else {
      return 'Failure: $_error';
    }
  }
}
