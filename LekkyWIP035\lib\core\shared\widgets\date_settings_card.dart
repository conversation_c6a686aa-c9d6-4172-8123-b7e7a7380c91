// File: lib/features/setup/presentation/widgets/date_settings_card.dart
import 'package:flutter/material.dart';
import '../models/date_format.dart';
import 'settings_section_header.dart';
import 'settings_radio.dart';

/// A widget for configuring date settings
class DateSettingsCard extends StatelessWidget {
  /// Date format
  final DateFormat dateFormat;

  /// Whether to show time with date
  final bool showTimeWithDate;

  /// Callback when date format changes
  final Function(DateFormat) onDateFormatChanged;

  /// Callback when show time with date changes
  final Function(bool) onShowTimeWithDateChanged;

  /// Constructor
  const DateSettingsCard({
    super.key,
    required this.dateFormat,
    required this.showTimeWithDate,
    required this.onDateFormatChanged,
    required this.onShowTimeWithDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SettingsSectionHeader(
              title: 'Date Settings',
              description:
                  'Choose how dates will be displayed throughout the app.',
              icon: Icons.calendar_today,
            ),

            // Date Format Subsection
            const Text(
              'Date Format',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            SettingsRadio<DateFormat>(
              title: 'Date Format',
              value: dateFormat,
              onChanged: onDateFormatChanged,
              options: const [
                SettingsRadioOption<DateFormat>(
                  value: DateFormat.ddMmYyyy,
                  title: 'DD-MM-YYYY',
                  description: 'Example: 11-05-2025',
                ),
                SettingsRadioOption<DateFormat>(
                  value: DateFormat.mmDdYyyy,
                  title: 'MM-DD-YYYY',
                  description: 'Example: 05-11-2025',
                ),
                SettingsRadioOption<DateFormat>(
                  value: DateFormat.yyyyMmDd,
                  title: 'YYYY-MM-DD',
                  description: 'Example: 2025-05-11',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Show Time with Date Subsection
            const Text(
              'Time Display',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Choose whether to show time alongside dates.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildShowTimeWithDateToggle(context),
          ],
        ),
      ),
    );
  }

  Widget _buildShowTimeWithDateToggle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Show time with date',
          style: TextStyle(fontSize: 16),
        ),
        Switch(
          value: showTimeWithDate,
          onChanged: onShowTimeWithDateChanged,
          activeColor: Theme.of(context).colorScheme.primary,
        ),
      ],
    );
  }
}