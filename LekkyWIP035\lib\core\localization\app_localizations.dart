import 'package:flutter/material.dart';
import '../constants/currency_constants.dart';

/// Supported locales for the app
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// Static member to have a simple access to the delegate from the MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Map of localized values
  static final Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'appName': 'Lekky',
      'tagline': 'Your Prepaid Meter Assistant',
      'splashQuote': 'I\'m not cheap—I\'m kilowatt-conscious.',
      'checkingPermissions': 'Checking permissions...',
      'initializing': 'Initializing...',
      'welcomeTitle': 'Welcome to <PERSON><PERSON><PERSON>',
      'welcomeSubtitle': 'Your personal prepaid meter assistant',
      'trackUsage': 'Track Your Usage',
      'getAlerts': 'Get Timely Alerts',
      'viewHistory': 'View History',
      'calculateCosts': 'Calculate Costs',
      'trackUsageDesc': 'Monitor your electricity consumption and spending',
      'getAlertsDesc': 'Receive notifications when your balance is running low',
      'viewHistoryDesc': 'See your past meter readings and top-ups',
      'calculateCostsDesc':
          'Estimate your electricity costs over different periods',
      'getStarted': 'Get Started',
      'restoreData': 'Restore Previous Data',
      'restoreHelper': 'Have a backup from another device?',
      'restoreDataTitle': 'Restore Data',
      'restoreDataContent':
          'This feature will allow you to restore data from a backup file.',
      'cancel': 'Cancel',
      'chooseFile': 'Choose File',
      'regionSettings': 'Region Settings',
      'language': 'Language',
      'currency': 'Currency',
      'selectLanguage': 'Select your preferred language for the app interface.',
      'selectCurrency': 'Select the currency for your meter readings.',
      'currencyTip':
          'Tip: Select the currency that matches your electricity bills.',
      'perDay': '/day',
    },
    'es': {
      'appName': 'Lekky',
      'tagline': 'Tu asistente de medidor prepago',
      'splashQuote': 'No soy tacaño, soy consciente de los kilovatios.',
      'checkingPermissions': 'Comprobando permisos...',
      'initializing': 'Inicializando...',
      'welcomeTitle': 'Bienvenido a Lekky',
      'welcomeSubtitle': 'Tu asistente personal de medidor prepago',
      'trackUsage': 'Seguimiento de uso',
      'getAlerts': 'Recibir alertas',
      'viewHistory': 'Ver historial',
      'calculateCosts': 'Calcular costos',
      'trackUsageDesc': 'Monitorea tu consumo y gasto de electricidad',
      'getAlertsDesc': 'Recibe notificaciones cuando tu saldo esté bajo',
      'viewHistoryDesc': 'Mira tus lecturas de medidor y recargas anteriores',
      'calculateCostsDesc':
          'Estima tus costos de electricidad en diferentes períodos',
      'getStarted': 'Comenzar',
      'restoreData': 'Restaurar datos anteriores',
      'restoreHelper': '¿Tienes una copia de seguridad de otro dispositivo?',
      'restoreDataTitle': 'Restaurar datos',
      'restoreDataContent':
          'Esta función te permitirá restaurar datos desde un archivo de respaldo.',
      'cancel': 'Cancelar',
      'chooseFile': 'Elegir archivo',
      'regionSettings': 'Configuración regional',
      'language': 'Idioma',
      'currency': 'Moneda',
      'selectLanguage':
          'Selecciona tu idioma preferido para la interfaz de la aplicación.',
      'selectCurrency': 'Selecciona la moneda para tus lecturas de medidor.',
      'currencyTip':
          'Consejo: Selecciona la moneda que coincida con tus facturas de electricidad.',
      'perDay': '/día',
    },
    // Add more languages as needed
  };

  /// Get localized string
  String translate(String key) {
    // Default to English if the locale or key is not found
    return _localizedValues[locale.languageCode]?[key] ??
        _localizedValues['en']?[key] ??
        key;
  }
}

/// Localization delegate
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Use centralized language codes
    return RegionalConstants.supportedLanguageCodes
        .contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
