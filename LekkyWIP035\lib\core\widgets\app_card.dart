// File: lib/core/widgets/app_card.dart
import 'package:flutter/material.dart';

/// A reusable card widget with consistent styling for the Lekky app
class AppCard extends StatelessWidget {
  /// The child widget to display inside the card
  final Widget child;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Optional padding for the card content (defaults to 16.0 on all sides)
  final EdgeInsetsGeometry padding;

  /// Optional margin for the card (defaults to 0.0)
  final EdgeInsetsGeometry margin;

  /// Optional elevation for the card (defaults to 4.0)
  final double elevation;

  /// Optional border radius for the card (defaults to 12.0)
  final double borderRadius;

  /// Optional background color for the card (defaults to Theme.cardColor)
  final Color? color;

  /// Optional border for the card
  final Border? border;

  /// Constructor
  const AppCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = EdgeInsets.zero,
    this.elevation = 4.0,
    this.borderRadius = 12.0,
    this.color,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidget = Card(
      margin: margin,
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: border != null
            ? BorderSide(
                color: border!.top.color,
                width: border!.top.width,
              )
            : BorderSide.none,
      ),
      color: color ?? Theme.of(context).cardColor,
      child: Padding(
        padding: padding,
        child: child,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: cardWidget,
      );
    }

    return cardWidget;
  }
}
