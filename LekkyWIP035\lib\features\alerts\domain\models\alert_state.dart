/// Status of the alert system
enum AlertStatus {
  idle,
  checking,
  scheduled,
  fired,
  error,
}

/// Types of alerts
enum AlertType {
  lowBalance,
  alertThreshold,
}

/// Immutable state for alert management
class AlertState {
  /// Current alert status
  final AlertStatus status;
  
  /// Whether alerts are enabled
  final bool enabled;
  
  /// Low balance alert settings
  final bool lowBalanceEnabled;
  
  /// Alert threshold settings
  final bool alertThresholdEnabled;
  final double alertThreshold;
  final int daysInAdvance;
  
  /// Current alert predictions
  final DateTime? nextLowBalanceAlert;
  final DateTime? nextThresholdAlert;
  
  /// Scheduled alert IDs for cancellation
  final int? scheduledLowBalanceId;
  final int? scheduledThresholdId;
  
  /// Current error state
  final AlertError? error;
  
  /// Event history buffer (last 5 events)
  final List<AlertEvent> eventHistory;
  
  /// Last successful check timestamp
  final DateTime? lastSuccessfulCheck;

  const AlertState({
    this.status = AlertStatus.idle,
    this.enabled = false,
    this.lowBalanceEnabled = false,
    this.alertThresholdEnabled = false,
    this.alertThreshold = 10.0,
    this.daysInAdvance = 3,
    this.nextLowBalanceAlert,
    this.nextThresholdAlert,
    this.scheduledLowBalanceId,
    this.scheduledThresholdId,
    this.error,
    this.eventHistory = const [],
    this.lastSuccessfulCheck,
  });

  /// Initial state
  factory AlertState.initial() => const AlertState();

  /// Create a copy with updated fields
  AlertState copyWith({
    AlertStatus? status,
    bool? enabled,
    bool? lowBalanceEnabled,
    bool? alertThresholdEnabled,
    double? alertThreshold,
    int? daysInAdvance,
    DateTime? nextLowBalanceAlert,
    DateTime? nextThresholdAlert,
    int? scheduledLowBalanceId,
    int? scheduledThresholdId,
    AlertError? error,
    List<AlertEvent>? eventHistory,
    DateTime? lastSuccessfulCheck,
  }) {
    return AlertState(
      status: status ?? this.status,
      enabled: enabled ?? this.enabled,
      lowBalanceEnabled: lowBalanceEnabled ?? this.lowBalanceEnabled,
      alertThresholdEnabled: alertThresholdEnabled ?? this.alertThresholdEnabled,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      daysInAdvance: daysInAdvance ?? this.daysInAdvance,
      nextLowBalanceAlert: nextLowBalanceAlert ?? this.nextLowBalanceAlert,
      nextThresholdAlert: nextThresholdAlert ?? this.nextThresholdAlert,
      scheduledLowBalanceId: scheduledLowBalanceId ?? this.scheduledLowBalanceId,
      scheduledThresholdId: scheduledThresholdId ?? this.scheduledThresholdId,
      error: error ?? this.error,
      eventHistory: eventHistory ?? this.eventHistory,
      lastSuccessfulCheck: lastSuccessfulCheck ?? this.lastSuccessfulCheck,
    );
  }
}

/// Extension methods for AlertState
extension AlertStateExtension on AlertState {
  /// Check if any alerts are active
  bool get hasActiveAlerts => 
      (lowBalanceEnabled && nextLowBalanceAlert != null) ||
      (alertThresholdEnabled && nextThresholdAlert != null);
  
  /// Check if alert system is in error state
  bool get hasError => error != null;
  
  /// Check if alert system is currently processing
  bool get isProcessing => status == AlertStatus.checking;
  
  /// Get the most recent event
  AlertEvent? get latestEvent => 
      eventHistory.isNotEmpty ? eventHistory.last : null;
  
  /// Get user-friendly status message
  String get statusMessage {
    switch (status) {
      case AlertStatus.idle:
        return enabled ? 'Ready' : 'Disabled';
      case AlertStatus.checking:
        return 'Checking alert conditions...';
      case AlertStatus.scheduled:
        return 'Alerts scheduled';
      case AlertStatus.fired:
        return 'Alert fired';
      case AlertStatus.error:
        return error?.userMessage ?? 'Error occurred';
    }
  }
  
  /// Add event to history with automatic pruning
  AlertState addEvent(AlertEvent event) {
    final newHistory = [...eventHistory, event];
    
    // Keep only last 5 events
    if (newHistory.length > 5) {
      newHistory.removeRange(0, newHistory.length - 5);
    }
    
    return copyWith(eventHistory: newHistory);
  }
  
  /// Clear error state
  AlertState clearError() {
    return copyWith(error: null);
  }
  
  /// Reset to initial state while preserving settings
  AlertState reset() {
    return copyWith(
      status: AlertStatus.idle,
      nextLowBalanceAlert: null,
      nextThresholdAlert: null,
      error: null,
      scheduledLowBalanceId: null,
      scheduledThresholdId: null,
    );
  }
}

/// Alert error types
enum AlertErrorType {
  dashboardDataUnavailable,
  calculationError,
  schedulingError,
  permissionDenied,
  serviceUnavailable,
  unknown,
}

/// Alert error severity
enum AlertErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Immutable alert error
class AlertError {
  final AlertErrorType type;
  final AlertErrorSeverity severity;
  final String userMessage;
  final String? technicalDetails;
  final DateTime timestamp;
  final bool shouldPersist;
  final List<String> recoveryActions;

  const AlertError({
    required this.type,
    required this.severity,
    required this.userMessage,
    this.technicalDetails,
    required this.timestamp,
    required this.shouldPersist,
    this.recoveryActions = const [],
  });

  /// Create dashboard data unavailable error
  factory AlertError.dashboardDataUnavailable() {
    return AlertError(
      type: AlertErrorType.dashboardDataUnavailable,
      severity: AlertErrorSeverity.medium,
      userMessage: 'Dashboard data not available for alert calculation',
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Add some meter readings',
        'Check data integrity',
        'Try again later',
      ],
    );
  }

  /// Create calculation error
  factory AlertError.calculationError({required String details}) {
    return AlertError(
      type: AlertErrorType.calculationError,
      severity: AlertErrorSeverity.medium,
      userMessage: 'Error calculating alert conditions',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Check meter reading data',
        'Verify alert settings',
        'Try again',
      ],
    );
  }

  /// Create scheduling error
  factory AlertError.schedulingError({required String details}) {
    return AlertError(
      type: AlertErrorType.schedulingError,
      severity: AlertErrorSeverity.high,
      userMessage: 'Failed to schedule alert notification',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Check notification permissions',
        'Restart the app',
        'Try again',
      ],
    );
  }

  /// Create unknown error
  factory AlertError.unknown({required String details}) {
    return AlertError(
      type: AlertErrorType.unknown,
      severity: AlertErrorSeverity.medium,
      userMessage: 'An unexpected error occurred',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again',
        'Restart the app if problem persists',
      ],
    );
  }

  /// Check if error can be retried
  bool get canRetry => true;
  
  /// Get primary recovery action
  String? get primaryRecoveryAction => 
      recoveryActions.isNotEmpty ? recoveryActions.first : null;
}

/// Alert event types
enum AlertEventType {
  checked,
  scheduled,
  fired,
  cancelled,
  error,
  settingsChanged,
}

/// Immutable alert event
class AlertEvent {
  final AlertEventType type;
  final AlertType? alertType;
  final DateTime timestamp;
  final String message;
  final Map<String, dynamic> metadata;

  const AlertEvent({
    required this.type,
    this.alertType,
    required this.timestamp,
    required this.message,
    this.metadata = const {},
  });

  /// Create checked event
  factory AlertEvent.checked({
    required int alertsFound,
  }) {
    return AlertEvent(
      type: AlertEventType.checked,
      timestamp: DateTime.now(),
      message: alertsFound > 0 
          ? 'Found $alertsFound alert condition(s)'
          : 'No alert conditions found',
      metadata: {
        'alerts_found': alertsFound,
        'checked_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create scheduled event
  factory AlertEvent.scheduled({
    required AlertType alertType,
    required DateTime scheduledTime,
  }) {
    final alertName = alertType == AlertType.lowBalance ? 'Low Balance' : 'Alert Threshold';
    return AlertEvent(
      type: AlertEventType.scheduled,
      alertType: alertType,
      timestamp: DateTime.now(),
      message: '$alertName alert scheduled',
      metadata: {
        'alert_type': alertType.toString(),
        'scheduled_time': scheduledTime.toIso8601String(),
        'scheduled_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create fired event
  factory AlertEvent.fired({
    required AlertType alertType,
  }) {
    final alertName = alertType == AlertType.lowBalance ? 'Low Balance' : 'Alert Threshold';
    return AlertEvent(
      type: AlertEventType.fired,
      alertType: alertType,
      timestamp: DateTime.now(),
      message: '$alertName alert fired',
      metadata: {
        'alert_type': alertType.toString(),
        'fired_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create error event
  factory AlertEvent.error({
    required String errorMessage,
  }) {
    return AlertEvent(
      type: AlertEventType.error,
      timestamp: DateTime.now(),
      message: 'Error: $errorMessage',
      metadata: {
        'error_at': DateTime.now().toIso8601String(),
        'error_message': errorMessage,
      },
    );
  }

  /// Create settings changed event
  factory AlertEvent.settingsChanged({
    required Map<String, dynamic> changes,
  }) {
    final changesList = changes.entries
        .map((e) => '${e.key}: ${e.value}')
        .join(', ');
    
    return AlertEvent(
      type: AlertEventType.settingsChanged,
      timestamp: DateTime.now(),
      message: 'Settings updated: $changesList',
      metadata: {
        'changed_at': DateTime.now().toIso8601String(),
        'changes': changes,
      },
    );
  }
}
