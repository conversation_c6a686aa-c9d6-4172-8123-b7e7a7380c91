// File: lib/core/utils/bulk_operation_context.dart
import 'dart:async';
import 'logger.dart';

/// Context for managing bulk operations to prevent event flooding
class BulkOperationContext {
  static bool _isBulkOperation = false;
  static final Completer<void> _operationCompleter = Completer<void>();
  static int _operationCount = 0;

  /// Check if currently in a bulk operation
  static bool get isBulkOperation => _isBulkOperation;

  /// Get current operation count for monitoring
  static int get operationCount => _operationCount;

  /// Start a bulk operation context
  static void startBulkOperation() {
    if (_isBulkOperation) {
      Logger.warning(
          'BulkOperationContext: Bulk operation already in progress');
      return;
    }

    _isBulkOperation = true;
    _operationCount = 0;
    Logger.info('BulkOperationContext: Started bulk operation');
  }

  /// End a bulk operation context
  static void endBulkOperation() {
    if (!_isBulkOperation) {
      Logger.warning('BulkOperationContext: No bulk operation in progress');
      return;
    }

    _isBulkOperation = false;
    Logger.info(
        'BulkOperationContext: Ended bulk operation with $_operationCount operations');

    if (!_operationCompleter.isCompleted) {
      _operationCompleter.complete();
    }
  }

  /// Increment operation count during bulk operations
  static void incrementOperationCount() {
    if (_isBulkOperation) {
      _operationCount++;
    }
  }

  /// Wait for bulk operation to complete
  static Future<void> waitForCompletion() async {
    if (_isBulkOperation) {
      await _operationCompleter.future;
    }
  }

  /// Reset context (for testing or error recovery)
  static void reset() {
    _isBulkOperation = false;
    _operationCount = 0;
    Logger.info('BulkOperationContext: Reset context');
  }
}
