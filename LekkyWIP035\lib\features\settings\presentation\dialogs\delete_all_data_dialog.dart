import 'package:flutter/material.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/services/data_deletion_service.dart';

/// Dialog for confirming deletion of all data
class DeleteAllDataDialog extends StatelessWidget {
  /// Data counts to display
  final DataCounts dataCounts;

  /// Constructor
  const DeleteAllDataDialog({
    super.key,
    required this.dataCounts,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDialogHeader(context),
            const SizedBox(height: 16),
            _buildWarningText(context),
            const SizedBox(height: 24),
            _buildDataSummary(context),
            const SizedBox(height: 24),
            _buildButtonBar(context),
          ],
        ),
      ),
    );
  }

  /// Build dialog header with warning icon
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.warning,
          color: Colors.red,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Delete All Data',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.of(context).pop(false),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build warning text
  Widget _buildWarningText(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'Are you sure you want to delete all your data? This action cannot be undone and will permanently remove all your meter readings and top-ups.',
      style: TextStyle(
        fontSize: 16,
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
    );
  }

  /// Build data summary showing what will be deleted
  Widget _buildDataSummary(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.delete_forever,
                color: Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Data to be deleted:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildDataRow(
            context,
            Icons.speed,
            'Meter Readings',
            dataCounts.meterReadings,
          ),
          const SizedBox(height: 8),
          _buildDataRow(
            context,
            Icons.add_card,
            'Top-ups',
            dataCounts.topUps,
          ),
          const SizedBox(height: 12),
          Divider(color: Colors.red.withOpacity(0.3)),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Entries:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Text(
                '${dataCounts.total}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build individual data row
  Widget _buildDataRow(
    BuildContext context,
    IconData icon,
    String label,
    int count,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 18,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        Text(
          '$count',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  /// Build button bar with Cancel and Delete All buttons
  Widget _buildButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(false),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Delete All',
            type: LekkyButtonType.destructive,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ),
      ],
    );
  }
}
