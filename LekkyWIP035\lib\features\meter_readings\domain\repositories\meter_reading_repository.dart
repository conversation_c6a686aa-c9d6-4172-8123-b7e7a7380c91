import '../models/meter_reading.dart';

/// Repository interface for meter readings
abstract class MeterReadingRepository {
  /// Get all meter readings
  Future<List<MeterReading>> getAllMeterReadings();
  
  /// Get meter readings with pagination
  Future<List<MeterReading>> getMeterReadings({
    required int page,
    required int pageSize,
  });
  
  /// Get meter reading by ID
  Future<MeterReading?> getMeterReadingById(int id);
  
  /// Get meter readings by date range
  Future<List<MeterReading>> getMeterReadingsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });
  
  /// Get latest meter reading
  Future<MeterReading?> getLatestMeterReading();
  
  /// Add a new meter reading
  Future<int> addMeterReading(MeterReading meterReading);
  
  /// Update an existing meter reading
  Future<int> updateMeterReading(MeterReading meterReading);
  
  /// Delete a meter reading
  Future<int> deleteMeterReading(int id);
  
  /// Get the count of meter readings
  Future<int> getMeterReadingsCount();
  
  /// Get invalid meter readings
  Future<List<MeterReading>> getInvalidMeterReadings();
  
  /// Validate a meter reading
  Future<bool> validateMeterReading(MeterReading meterReading);
}
