import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger.dart';

/// Platform information data class
class PlatformInfo {
  final bool isAndroid;
  final bool isIOS;
  final int? androidSdkInt;
  final String? androidVersion;
  final String? iosVersion;
  final String platformName;
  final String displayName;

  const PlatformInfo({
    required this.isAndroid,
    required this.isIOS,
    this.androidSdkInt,
    this.androidVersion,
    this.iosVersion,
    required this.platformName,
    required this.displayName,
  });

  /// Get user-friendly platform description
  String get userFriendlyDescription {
    if (isAndroid && androidSdkInt != null) {
      if (androidSdkInt! >= 34) return 'Android 14+';
      if (androidSdkInt! >= 33) return 'Android 13';
      if (androidSdkInt! >= 29) return 'Android 10-12';
      if (androidSdkInt! >= 26) return 'Android 8.0-9.0';
      if (androidSdkInt! >= 21) return 'Android 5.0-7.1';
      return 'Android (Legacy)';
    }
    if (isIOS) return 'iOS';
    return 'Unknown Platform';
  }

  /// Check if platform supports modern file access
  bool get supportsModernFileAccess {
    if (isIOS) return true;
    if (isAndroid && androidSdkInt != null) {
      return androidSdkInt! >= 29; // Android 10+
    }
    return false;
  }

  /// Check if platform supports Storage Access Framework
  bool get supportsSAF {
    if (isIOS) return true;
    if (isAndroid && androidSdkInt != null) {
      return androidSdkInt! >= 19; // Android 4.4+
    }
    return false;
  }

  /// Check if platform requires legacy file access
  bool get requiresLegacyFileAccess {
    if (isAndroid && androidSdkInt != null) {
      return androidSdkInt! < 29; // Below Android 10
    }
    return false;
  }
}

/// Platform service for centralized platform detection and capabilities
class PlatformService {
  static PlatformInfo? _cachedInfo;

  /// Get current platform information
  static Future<PlatformInfo> getPlatformInfo() async {
    if (_cachedInfo != null) {
      return _cachedInfo!;
    }

    try {
      Logger.info('Detecting platform information...');
      
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        
        _cachedInfo = PlatformInfo(
          isAndroid: true,
          isIOS: false,
          androidSdkInt: androidInfo.version.sdkInt,
          androidVersion: androidInfo.version.release,
          platformName: 'android',
          displayName: 'Android ${androidInfo.version.release}',
        );
        
        Logger.info('Android detected: API ${androidInfo.version.sdkInt}, Version ${androidInfo.version.release}');
      } else if (Platform.isIOS) {
        final deviceInfo = DeviceInfoPlugin();
        final iosInfo = await deviceInfo.iosInfo;
        
        _cachedInfo = PlatformInfo(
          isAndroid: false,
          isIOS: true,
          iosVersion: iosInfo.systemVersion,
          platformName: 'ios',
          displayName: 'iOS ${iosInfo.systemVersion}',
        );
        
        Logger.info('iOS detected: Version ${iosInfo.systemVersion}');
      } else {
        _cachedInfo = const PlatformInfo(
          isAndroid: false,
          isIOS: false,
          platformName: 'unknown',
          displayName: 'Unknown Platform',
        );
        
        Logger.warning('Unknown platform detected');
      }

      return _cachedInfo!;
    } catch (e) {
      Logger.error('Error detecting platform: $e');
      
      // Fallback to basic platform detection
      _cachedInfo = PlatformInfo(
        isAndroid: Platform.isAndroid,
        isIOS: Platform.isIOS,
        platformName: Platform.isAndroid ? 'android' : Platform.isIOS ? 'ios' : 'unknown',
        displayName: Platform.isAndroid ? 'Android' : Platform.isIOS ? 'iOS' : 'Unknown',
      );
      
      return _cachedInfo!;
    }
  }

  /// Clear cached platform info (for testing)
  static void clearCache() {
    _cachedInfo = null;
  }
}

/// Riverpod provider for platform information
final platformInfoProvider = FutureProvider<PlatformInfo>((ref) async {
  return await PlatformService.getPlatformInfo();
});

/// Riverpod provider for platform capabilities
final platformCapabilitiesProvider = Provider<AsyncValue<Map<String, bool>>>((ref) {
  final platformAsync = ref.watch(platformInfoProvider);
  
  return platformAsync.when(
    data: (platform) => AsyncValue.data({
      'supportsModernFileAccess': platform.supportsModernFileAccess,
      'supportsSAF': platform.supportsSAF,
      'requiresLegacyFileAccess': platform.requiresLegacyFileAccess,
      'isAndroid': platform.isAndroid,
      'isIOS': platform.isIOS,
    }),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});
