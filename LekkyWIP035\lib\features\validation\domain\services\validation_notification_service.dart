import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../notifications/data/notification_service.dart';
import '../../../notifications/domain/models/notification.dart';
import 'data_integrity_service.dart';

/// Service for managing validation-related notifications with batching
class ValidationNotificationService {
  static final ValidationNotificationService _instance =
      ValidationNotificationService._internal();

  factory ValidationNotificationService() => _instance;
  ValidationNotificationService._internal();

  Timer? _batchTimer;
  final List<String> _pendingInvalidEntries = [];
  static const int _batchTimeoutMs = 3000; // 3 seconds for robust UX

  /// Trigger invalid entry notification with batching
  Future<void> triggerInvalidEntryNotification(String entryDetails) async {
    try {
      // Check if invalid record alerts are enabled
      if (!await _areInvalidRecordAlertsEnabled()) {
        Logger.info(
            'ValidationNotificationService: Invalid record alerts disabled, skipping notification');
        return;
      }

      // Add to pending notifications
      _pendingInvalidEntries.add(entryDetails);

      // Cancel existing timer
      _batchTimer?.cancel();

      // Start new batch timer
      _batchTimer = Timer(const Duration(milliseconds: _batchTimeoutMs), () {
        _processBatchedNotifications();
      });

      Logger.info(
          'ValidationNotificationService: Added invalid entry to batch. Total pending: ${_pendingInvalidEntries.length}');
    } catch (e) {
      Logger.error('Failed to trigger invalid entry notification: $e');
    }
  }

  /// Process batched invalid entry notifications
  Future<void> _processBatchedNotifications() async {
    if (_pendingInvalidEntries.isEmpty) return;

    try {
      final entryCount = _pendingInvalidEntries.length;
      Logger.info(
          'ValidationNotificationService: Processing batch of $entryCount invalid entries');

      // Create notification message with details and reference
      String message;
      if (entryCount == 1) {
        message =
            'Invalid record detected: ${_pendingInvalidEntries.first}. Check validation dashboard for details.';
      } else {
        final details = _pendingInvalidEntries.take(3).join(', ');
        final remaining = entryCount > 3 ? ' and ${entryCount - 3} more' : '';
        message =
            '$entryCount invalid records detected: $details$remaining. Check validation dashboard for full details.';
      }

      // Create notification
      final notification = AppNotification(
        title: entryCount == 1
            ? 'Invalid Record Detected'
            : 'Invalid Records Detected',
        message: message,
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      );

      // Send notification through notification service
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      await notificationService.showNotification(notification);

      // Fire event to trigger immediate provider refresh
      EventBus().fire(EventType.notificationCreated);

      // Clear pending entries
      _pendingInvalidEntries.clear();

      Logger.info(
          'ValidationNotificationService: Batch notification sent successfully');
    } catch (e) {
      Logger.error('Failed to process batched notifications: $e');
      _pendingInvalidEntries.clear();
    }
  }

  /// Check if invalid entry notifications are enabled
  Future<bool> _areInvalidEntryNotificationsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final invalidRecordAlertsEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;

      // Only check specific type (master switch removed)
      return invalidRecordAlertsEnabled;
    } catch (e) {
      Logger.error('Failed to check notification settings: $e');
      return false;
    }
  }

  /// Trigger immediate validation check for entry
  Future<void> validateAndNotifyIfInvalid(dynamic entry) async {
    try {
      // Check if notifications are enabled
      if (!await _areInvalidEntryNotificationsEnabled()) {
        return;
      }

      // Validate the entry
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      final issues = await dataIntegrityService.validateSingleEntry(entry);

      if (issues.isNotEmpty) {
        // Create entry details for notification
        final entryType = entry.runtimeType.toString();
        final entryDate =
            entry.date?.toString().split(' ')[0] ?? 'Unknown date';
        final entryValue = entry.value?.toString() ??
            entry.amount?.toString() ??
            'Unknown value';

        final details = '$entryType on $entryDate (£$entryValue)';

        // Trigger batched notification
        await triggerInvalidEntryNotification(details);
      }
    } catch (e) {
      Logger.error('Failed to validate and notify for entry: $e');
    }
  }

  /// Cancel any pending batch notifications
  void cancelPendingNotifications() {
    _batchTimer?.cancel();
    _pendingInvalidEntries.clear();
    Logger.info(
        'ValidationNotificationService: Cancelled pending notifications');
  }

  /// Check if invalid record alerts are enabled in settings
  Future<bool> _areInvalidRecordAlertsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
    } catch (e) {
      Logger.error('Failed to check invalid record alert settings: $e');
      return false; // Default to disabled if settings can't be read
    }
  }
}
