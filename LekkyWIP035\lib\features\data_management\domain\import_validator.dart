// File: lib/features/data_management/domain/import_validator.dart
import 'dart:async';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';

/// A validator for imported data
class ImportValidator {
  /// Logger instance
  final logger = Logger('ImportValidator');

  /// Validate a list of meter entries
  ///
  /// Returns a list of validation issues
  Future<List<String>> validateEntries(List<MeterEntry> entries) async {
    final issues = <String>[];

    // Check if there are any entries
    if (entries.isEmpty) {
      issues.add('No entries found in the import file');
      return issues;
    }

    // Check for chronological order
    bool chronologicalOrder = true;
    for (int i = 1; i < entries.length; i++) {
      if (entries[i].timestamp.isBefore(entries[i - 1].timestamp)) {
        chronologicalOrder = false;
        break;
      }
    }

    if (!chronologicalOrder) {
      issues.add('Entries are not in chronological order');
    }

    // Check for negative values
    bool hasNegativeValues = entries
        .any((entry) => (entry.reading < 0) || (entry.amountToppedUp < 0));

    if (hasNegativeValues) {
      issues.add('Some entries have negative values');
    }

    // Check for future dates
    final now = DateTime.now();
    bool hasFutureDates = entries.any((entry) => entry.timestamp.isAfter(now));

    if (hasFutureDates) {
      issues.add('Some entries have future dates');
    }

    // Check for logical sequence in meter readings
    final meterReadings = entries.where((e) => e.isReading).toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    bool logicalSequence = true;
    for (int i = 1; i < meterReadings.length; i++) {
      if (meterReadings[i].reading > meterReadings[i - 1].reading) {
        logicalSequence = false;
        break;
      }
    }

    if (!logicalSequence && meterReadings.length > 1) {
      issues.add('Meter readings are not decreasing over time');
    }

    // Check for duplicate dates
    final dateMap = <String, int>{};
    for (final entry in entries) {
      final dateStr = entry.timestamp.toIso8601String().split('T')[0];
      dateMap[dateStr] = (dateMap[dateStr] ?? 0) + 1;
    }

    final duplicateDates =
        dateMap.entries.where((e) => e.value > 1).map((e) => e.key).toList();

    if (duplicateDates.isNotEmpty) {
      issues.add('Multiple entries found for the same date');
    }

    // Check for large gaps between entries
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    bool hasLargeGaps = false;
    for (int i = 1; i < sortedEntries.length; i++) {
      final gap = sortedEntries[i]
          .timestamp
          .difference(sortedEntries[i - 1].timestamp)
          .inDays;
      if (gap > 30) {
        hasLargeGaps = true;
        break;
      }
    }

    if (hasLargeGaps) {
      issues.add('Large gaps (>30 days) found between some entries');
    }

    // Check for balance consistency
    bool balanceConsistency = await _checkBalanceConsistency(entries);
    if (!balanceConsistency) {
      issues.add(
          'Balance inconsistencies detected between meter readings and top-ups');
    }

    return issues;
  }

  /// Validate a batch of entries against existing entries
  ///
  /// Returns a list of validation issues
  Future<List<String>> validateBatch(
      List<MeterEntry> newEntries, List<MeterEntry> existingEntries) async {
    final issues = <String>[];

    // First validate the new entries on their own
    issues.addAll(await validateEntries(newEntries));

    // Check for duplicate entries (same date, type, and amount)
    final duplicates = findDuplicateEntries(newEntries, existingEntries);

    if (duplicates.isNotEmpty) {
      issues.add(
          '${duplicates.length} entries are duplicates of existing entries');
    }

    // Check for chronological consistency with existing entries
    if (existingEntries.isNotEmpty && newEntries.isNotEmpty) {
      final oldestNew = newEntries
          .map((e) => e.timestamp)
          .reduce((a, b) => a.isBefore(b) ? a : b);
      final newestExisting = existingEntries
          .map((e) => e.timestamp)
          .reduce((a, b) => a.isAfter(b) ? a : b);

      if (oldestNew.isBefore(newestExisting)) {
        issues.add('New entries overlap with existing entries in time');
      }
    }

    // Check for balance consistency across both sets
    final combinedEntries = [...existingEntries, ...newEntries]
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    bool balanceConsistency = await _checkBalanceConsistency(combinedEntries);
    if (!balanceConsistency) {
      issues.add(
          'Balance inconsistencies detected when combining with existing entries');
    }

    return issues;
  }

  /// Find duplicate entries between new and existing entries
  ///
  /// Returns a list of new entries that are duplicates of existing entries
  List<MeterEntry> findDuplicateEntries(
      List<MeterEntry> newEntries, List<MeterEntry> existingEntries) {
    final duplicates = <MeterEntry>[];

    for (final newEntry in newEntries) {
      for (final existingEntry in existingEntries) {
        if (_isDuplicate(newEntry, existingEntry)) {
          duplicates.add(newEntry);
          break; // Found a duplicate, no need to check further
        }
      }
    }

    return duplicates;
  }

  /// Check if two entries are duplicates
  ///
  /// Entries are considered duplicates if they have the same date, type, and amount
  bool _isDuplicate(MeterEntry entry1, MeterEntry entry2) {
    return entry1.timestamp.isAtSameMomentAs(entry2.timestamp) &&
        entry1.typeCode == entry2.typeCode &&
        entry1.value == entry2.value;
  }

  /// Check balance consistency between meter readings and top-ups
  ///
  /// Returns true if balance is consistent, false otherwise
  Future<bool> _checkBalanceConsistency(List<MeterEntry> entries) async {
    // Sort entries by timestamp
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Filter readings and top-ups
    final readings = sortedEntries.where((e) => e.isReading).toList();

    // If we have fewer than 2 readings, we can't check consistency
    if (readings.length < 2) {
      return true;
    }

    // Check each pair of consecutive readings
    for (int i = 1; i < readings.length; i++) {
      final prevReading = readings[i - 1];
      final currReading = readings[i];

      // Find top-ups between these readings
      final topUpsBetween = sortedEntries
          .where((e) =>
              e.isTopUp &&
              e.timestamp.isAfter(prevReading.timestamp) &&
              e.timestamp.isBefore(currReading.timestamp))
          .toList();

      // Calculate total top-up amount
      final totalTopUp =
          topUpsBetween.fold<double>(0, (sum, e) => sum + e.amountToppedUp);

      // Calculate expected current reading
      final expectedReading = prevReading.reading - totalTopUp;

      // Check if current reading is significantly different from expected
      // Allow for some small rounding errors (0.01)
      if ((currReading.reading - expectedReading).abs() > 0.01) {
        return false;
      }
    }

    return true;
  }
}
