// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_evaluator_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationEvaluatorHash() =>
    r'8f5e5c5e5c5e5c5e5c5e5c5e5c5e5c5e5c5e5c5e';

/// Provider that evaluates notification conditions using dashboard calculations
///
/// Copied from [NotificationEvaluator].
@ProviderFor(NotificationEvaluator)
final notificationEvaluatorProvider = AutoDisposeAsyncNotifierProvider<
    NotificationEvaluator, List<AppNotification>>.internal(
  NotificationEvaluator.new,
  name: r'notificationEvaluatorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationEvaluatorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationEvaluator
    = AutoDisposeAsyncNotifier<List<AppNotification>>;
