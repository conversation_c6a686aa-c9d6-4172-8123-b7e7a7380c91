// Simplified version without freezed

/// Types of reminder events for history tracking
enum ReminderEventType {
  scheduled,
  fired,
  rescheduled,
  error,
  cancelled,
  settingsChanged,
}

/// Immutable reminder event for history tracking
class ReminderEvent {
  /// Type of event
  final ReminderEventType type;

  /// When the event occurred
  final DateTime timestamp;

  /// User-visible message describing the event
  final String message;

  /// Optional metadata for the event
  final Map<String, dynamic> metadata;

  /// Associated reminder date (for scheduled/fired events)
  final DateTime? reminderDate;

  /// Error details (for error events)
  final String? errorDetails;

  const ReminderEvent({
    required this.type,
    required this.timestamp,
    required this.message,
    this.metadata = const {},
    this.reminderDate,
    this.errorDetails,
  });

  /// Create a scheduled event
  factory ReminderEvent.scheduled({
    required DateTime reminderDate,
    required String frequency,
  }) {
    return ReminderEvent(
      type: ReminderEventType.scheduled,
      timestamp: DateTime.now(),
      message: 'Reminder scheduled for ${_formatReminderDate(reminderDate)}',
      reminderDate: reminderDate,
      metadata: {
        'frequency': frequency,
        'scheduled_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create a fired event
  factory ReminderEvent.fired({
    required DateTime reminderDate,
  }) {
    return ReminderEvent(
      type: ReminderEventType.fired,
      timestamp: DateTime.now(),
      message: 'Reminder fired successfully',
      reminderDate: reminderDate,
      metadata: {
        'fired_at': DateTime.now().toIso8601String(),
        'original_reminder_date': reminderDate.toIso8601String(),
      },
    );
  }

  /// Create a rescheduled event
  factory ReminderEvent.rescheduled({
    required DateTime newReminderDate,
    required String frequency,
  }) {
    return ReminderEvent(
      type: ReminderEventType.rescheduled,
      timestamp: DateTime.now(),
      message:
          'Next reminder scheduled for ${_formatReminderDate(newReminderDate)}',
      reminderDate: newReminderDate,
      metadata: {
        'frequency': frequency,
        'rescheduled_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create an error event
  factory ReminderEvent.error({
    required String errorMessage,
    String? errorDetails,
    Map<String, dynamic>? additionalMetadata,
  }) {
    return ReminderEvent(
      type: ReminderEventType.error,
      timestamp: DateTime.now(),
      message: 'Error: $errorMessage',
      errorDetails: errorDetails,
      metadata: {
        'error_at': DateTime.now().toIso8601String(),
        'error_type': errorMessage,
        if (additionalMetadata != null) ...additionalMetadata,
      },
    );
  }

  /// Create a cancelled event
  factory ReminderEvent.cancelled({
    String reason = 'User cancelled',
  }) {
    return ReminderEvent(
      type: ReminderEventType.cancelled,
      timestamp: DateTime.now(),
      message: 'Reminder cancelled: $reason',
      metadata: {
        'cancelled_at': DateTime.now().toIso8601String(),
        'reason': reason,
      },
    );
  }

  /// Create a settings changed event
  factory ReminderEvent.settingsChanged({
    required Map<String, dynamic> changes,
  }) {
    final changesList =
        changes.entries.map((e) => '${e.key}: ${e.value}').join(', ');

    return ReminderEvent(
      type: ReminderEventType.settingsChanged,
      timestamp: DateTime.now(),
      message: 'Settings updated: $changesList',
      metadata: {
        'changed_at': DateTime.now().toIso8601String(),
        'changes': changes,
      },
    );
  }

  /// Format reminder date for display
  static String _formatReminderDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays} days from now';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours from now';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes from now';
    } else {
      return 'now';
    }
  }
}

/// Extension methods for ReminderEvent
extension ReminderEventExtension on ReminderEvent {
  /// Get icon for event type
  String get icon {
    switch (type) {
      case ReminderEventType.scheduled:
        return '📅';
      case ReminderEventType.fired:
        return '🔔';
      case ReminderEventType.rescheduled:
        return '🔄';
      case ReminderEventType.error:
        return '❌';
      case ReminderEventType.cancelled:
        return '🚫';
      case ReminderEventType.settingsChanged:
        return '⚙️';
    }
  }

  /// Get color for event type (for UI)
  String get colorHex {
    switch (type) {
      case ReminderEventType.scheduled:
        return '#4CAF50'; // Green
      case ReminderEventType.fired:
        return '#2196F3'; // Blue
      case ReminderEventType.rescheduled:
        return '#FF9800'; // Orange
      case ReminderEventType.error:
        return '#F44336'; // Red
      case ReminderEventType.cancelled:
        return '#9E9E9E'; // Grey
      case ReminderEventType.settingsChanged:
        return '#9C27B0'; // Purple
    }
  }
}
