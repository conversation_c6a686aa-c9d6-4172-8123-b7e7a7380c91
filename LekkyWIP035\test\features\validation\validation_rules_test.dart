// File: test/features/validation/validation_rules_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/models/meter_entry.dart';
import '../../helpers/test_data_helper.dart';

/// Basic validation rules testing
class ValidationRules {
  static bool isPositiveValue(double value) => value >= 0;
  static bool isFutureDate(DateTime date) => date.isAfter(DateTime.now());
  static bool isValidMeterReading(MeterEntry entry) {
    return entry.typeCode == 0 &&
        isPositiveValue(entry.reading) &&
        !isFutureDate(entry.date);
  }

  static bool isValidTopUp(MeterEntry entry) {
    return entry.typeCode == 1 &&
        entry.amountToppedUp > 0 && // Top-ups must be greater than zero
        !isFutureDate(entry.date);
  }
}

void main() {
  group('Validation Rules', () {
    group('isPositiveValue', () {
      test('should return true for positive values', () {
        expect(ValidationRules.isPositiveValue(50.0), isTrue);
        expect(ValidationRules.isPositiveValue(0.0), isTrue);
        expect(ValidationRules.isPositiveValue(100.5), isTrue);
      });

      test('should return false for negative values', () {
        expect(ValidationRules.isPositiveValue(-10.0), isFalse);
        expect(ValidationRules.isPositiveValue(-0.01), isFalse);
        expect(ValidationRules.isPositiveValue(-100.5), isFalse);
      });
    });

    group('isFutureDate', () {
      test('should return false for past dates', () {
        final pastDate = DateTime.now().subtract(const Duration(days: 1));
        expect(ValidationRules.isFutureDate(pastDate), isFalse);
      });

      test('should return false for current date', () {
        final now = DateTime.now();
        // Note: This might occasionally fail due to timing, but generally should pass
        expect(ValidationRules.isFutureDate(now), isFalse);
      });

      test('should return true for future dates', () {
        final futureDate = DateTime.now().add(const Duration(days: 1));
        expect(ValidationRules.isFutureDate(futureDate), isTrue);
      });
    });

    group('isValidMeterReading', () {
      test('should validate positive meter reading with past date', () {
        // Arrange
        final entry = TestDataHelper.createMeterReading(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          reading: 50.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidMeterReading(entry), isTrue);
      });

      test('should reject negative meter reading', () {
        // Arrange
        final entry = TestDataHelper.createMeterReading(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          reading: -10.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidMeterReading(entry), isFalse);
      });

      test('should reject meter reading with future date', () {
        // Arrange
        final entry = TestDataHelper.createMeterReading(
          id: 1,
          date: DateTime.now().add(const Duration(days: 1)),
          reading: 50.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidMeterReading(entry), isFalse);
      });

      test('should accept zero meter reading', () {
        // Arrange
        final entry = TestDataHelper.createMeterReading(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          reading: 0.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidMeterReading(entry), isTrue);
      });
    });

    group('isValidTopUp', () {
      test('should validate positive top-up with past date', () {
        // Arrange
        final entry = TestDataHelper.createTopUp(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          amountToppedUp: 20.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidTopUp(entry), isTrue);
      });

      test('should reject negative top-up', () {
        // Arrange
        final entry = TestDataHelper.createTopUp(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          amountToppedUp: -5.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidTopUp(entry), isFalse);
      });

      test('should reject zero top-up', () {
        // Arrange
        final entry = TestDataHelper.createTopUp(
          id: 1,
          date: DateTime.now().subtract(const Duration(days: 1)),
          amountToppedUp: 0.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidTopUp(entry), isFalse);
      });

      test('should reject top-up with future date', () {
        // Arrange
        final entry = TestDataHelper.createTopUp(
          id: 1,
          date: DateTime.now().add(const Duration(days: 1)),
          amountToppedUp: 20.0,
        );

        // Act & Assert
        expect(ValidationRules.isValidTopUp(entry), isFalse);
      });
    });

    group('batch validation', () {
      test('should validate multiple entries efficiently', () {
        // Arrange
        final validEntries = TestDataHelper.createSampleMeterReadings();
        final stopwatch = Stopwatch()..start();

        // Act
        final results = validEntries.map((entry) {
          return entry.typeCode == 0
              ? ValidationRules.isValidMeterReading(entry)
              : ValidationRules.isValidTopUp(entry);
        }).toList();
        stopwatch.stop();

        // Assert
        expect(results.length, equals(validEntries.length));
        expect(results.every((r) => r == true), isTrue);
        expect(stopwatch.elapsedMilliseconds, lessThan(50)); // Should be fast
      });

      test('should identify invalid entries in mixed dataset', () {
        // Arrange
        final validEntries = TestDataHelper.createSampleMeterReadings();
        final invalidEntries = TestDataHelper.createInvalidEntries();
        final allEntries = [...validEntries, ...invalidEntries];

        // Act
        final results = allEntries.map((entry) {
          return entry.typeCode == 0
              ? ValidationRules.isValidMeterReading(entry)
              : ValidationRules.isValidTopUp(entry);
        }).toList();

        final invalidResults = results.where((r) => r == false).toList();

        // Assert
        expect(invalidResults.length, equals(invalidEntries.length));
      });
    });

    group('performance tests', () {
      test('should handle large dataset validation efficiently', () {
        // Arrange
        final largeDataset = TestDataHelper.createLargeDataset();
        final stopwatch = Stopwatch()..start();

        // Act
        final results = largeDataset.map((entry) {
          return entry.typeCode == 0
              ? ValidationRules.isValidMeterReading(entry)
              : ValidationRules.isValidTopUp(entry);
        }).toList();
        stopwatch.stop();

        // Assert
        expect(results.length, equals(630));
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });
    });
  });
}
