// File: lib/core/theme/table_styles.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Standardized table styles for consistent appearance across the app
class TableStyles {
  // Private constructor to prevent instantiation
  TableStyles._();

  /// Standard column spacing
  static const double columnSpacing = 16.0;

  /// Standard heading row height
  static const double headingRowHeight = 48.0;

  /// Standard data row height (min and max are the same for consistency)
  static const double dataRowHeight = 56.0;

  /// Standard divider thickness
  static const double dividerThickness = 1.0;

  /// Standard horizontal margin
  static const double horizontalMargin = 16.0;

  /// Standard border radius
  static const double borderRadius = 12.0;

  /// Standard cell padding
  static const double cellPadding = 8.0;

  /// Standard icon size
  static const double iconSize = 20.0;

  /// Get the standard heading row color based on theme
  static Color getHeadingRowColor(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return isDarkMode ? AppColors.tableHeaderDark : AppColors.tableHeaderLight;
  }

  /// Get the standard heading text style based on theme
  static TextStyle getHeadingTextStyle(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 16.0,
      color: isDarkMode
          ? AppColors.tableHeaderTextDark
          : AppColors.tableHeaderText,
    );
  }

  /// Get the standard row color for meter readings based on theme and validity
  static Color getReadingRowColor(
      BuildContext context, bool isValid, bool isEvenRow) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (!isValid) {
      return isDarkMode
          ? AppColors.tableRowInvalidDark
          : AppColors.tableRowInvalid;
    }

    if (isEvenRow) {
      return isDarkMode ? AppColors.tableRowEvenDark : AppColors.tableRowEven;
    } else {
      return isDarkMode ? AppColors.tableRowOddDark : AppColors.tableRowOdd;
    }
  }

  /// Get the standard row color for top-ups based on theme
  static Color getTopUpRowColor(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return isDarkMode ? AppColors.tableRowTopUpDark : AppColors.tableRowTopUp;
  }

  /// Get the standard text style for meter readings based on theme
  static TextStyle getReadingTextStyle(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 16.0,
      color: isDarkMode
          ? AppColors.tableReadingTextDark
          : AppColors.tableReadingText,
    );
  }

  /// Get the standard text style for top-ups based on theme
  static TextStyle getTopUpTextStyle(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 16.0,
      color:
          isDarkMode ? AppColors.tableTopUpTextDark : AppColors.tableTopUpText,
    );
  }

  /// Get the text style for an average value based on the current theme
  static TextStyle getAverageTextStyle(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      color: isDarkMode
          ? AppColors.tableAverageTextDark
          : AppColors.tableAverageText,
      fontSize: 14.0,
    );
  }

  /// Get the text style for a date based on the current theme
  static TextStyle getDateTextStyle(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      color: isDarkMode ? AppColors.tableDateTextDark : AppColors.tableDateText,
      fontSize: 14.0,
    );
  }

  /// Get the decoration for a table container based on the current theme
  static BoxDecoration getTableDecoration(BuildContext context,
      {bool isEditMode = false}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final theme = Theme.of(context);

    return BoxDecoration(
      color: isDarkMode ? AppColors.surfaceDark : AppColors.surface,
      borderRadius: BorderRadius.circular(borderRadius),
      border: isEditMode
          ? Border.all(color: theme.colorScheme.error, width: 2.0)
          : null,
      boxShadow: [
        BoxShadow(
          color: isDarkMode ? AppColors.shadowColorDark : AppColors.shadowColor,
          blurRadius: 4,
          spreadRadius: 1,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }
}
