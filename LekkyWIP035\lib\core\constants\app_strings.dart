/// App string constants used throughout the application
class AppStrings {
  // App Info
  /// The name of the app
  static const appName = '<PERSON><PERSON><PERSON>';
  
  // Splash Screen
  /// Tagline displayed on the splash screen
  static const tagline = 'Your Prepaid Meter Assistant';
  /// Quote displayed on the splash screen
  static const splashQuote = 'I\'m not cheap—I\'m kilowatt-conscious.';
  /// Status text displayed while checking permissions
  static const checkingPermissions = 'Checking permissions...';
  /// Status text displayed while initializing
  static const initializing = 'Initializing...';
  
  // Welcome Screen
  /// Title displayed on the welcome screen
  static const welcomeTitle = 'Welcome to Lek<PERSON>';
  /// Subtitle displayed on the welcome screen
  static const welcomeSubtitle = 'Your personal prepaid meter assistant';
  
  // Feature Titles
  /// Title for the track usage feature
  static const trackUsage = 'Track Your Usage';
  /// Title for the alerts feature
  static const getAlerts = 'Get Timely Alerts';
  /// Title for the history feature
  static const viewHistory = 'View History';
  /// Title for the cost calculation feature
  static const calculateCosts = 'Calculate Costs';
  
  // Feature Descriptions
  /// Description for the track usage feature
  static const trackUsageDesc = 'Monitor your electricity consumption and spending';
  /// Description for the alerts feature
  static const getAlertsDesc = 'Receive notifications when your balance is running low';
  /// Description for the history feature
  static const viewHistoryDesc = 'See your past meter readings and top-ups';
  /// Description for the cost calculation feature
  static const calculateCostsDesc = 'Estimate your electricity costs over different periods';
  
  // Buttons
  /// Text for the get started button
  static const getStarted = 'Get Started';
  /// Text for the restore data button
  static const restoreData = 'Restore Previous Data';
  /// Helper text for the restore data button
  static const restoreHelper = 'Have a backup from another device?';
  
  // Dialogs
  /// Title for the restore data dialog
  static const restoreDataTitle = 'Restore Data';
  /// Content for the restore data dialog
  static const restoreDataContent = 'This feature will allow you to restore data from a backup file.';
  /// Text for the cancel button
  static const cancel = 'Cancel';
  /// Text for the choose file button
  static const chooseFile = 'Choose File';
}
