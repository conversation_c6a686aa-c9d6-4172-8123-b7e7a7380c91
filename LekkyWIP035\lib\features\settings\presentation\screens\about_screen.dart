import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// About screen
class AboutScreen extends StatelessWidget {
  /// Constructor
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Banner with back arrow
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: AppBanner(
              message: '← App Information',
              gradientColors: AppColors.getSettingsMainCardGradient(
                  Theme.of(context).brightness == Brightness.dark),
              textColor: AppColors.getAppBarTextColor(
                  'settings', Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // App Info section
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue),
                            SizedBox(width: 16),
                            Text(
                              'App Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // App logo
                        Center(
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.electric_meter,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // App name and version
                        const Center(
                          child: Text(
                            'Lekky',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Center(
                          child: Text(
                            'Version 1.0.0',
                            style: TextStyle(
                              fontSize: 16,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // App description
                        const Text(
                          'Lekky is a prepaid electricity meter tracking app designed to help you monitor your electricity usage and manage your meter readings.',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                // Features section
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber),
                            SizedBox(width: 16),
                            Text(
                              'Features',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Features list
                        _buildFeatureItem(
                          Icons.track_changes,
                          'Track Meter Readings',
                          'Record and monitor your meter readings over time',
                        ),
                        _buildFeatureItem(
                          Icons.add_card,
                          'Record Top-ups',
                          'Keep track of all your meter top-ups',
                        ),
                        _buildFeatureItem(
                          Icons.calculate,
                          'Usage Calculations',
                          'Automatically calculate your daily usage',
                        ),
                        _buildFeatureItem(
                          Icons.notifications,
                          'Smart Notifications',
                          'Get alerts when your balance is running low',
                        ),
                        _buildFeatureItem(
                          Icons.history,
                          'History Tracking',
                          'View your complete usage history',
                        ),
                        _buildFeatureItem(
                          Icons.backup,
                          'Data Backup',
                          'Backup and restore your data',
                        ),
                      ],
                    ),
                  ),
                ),

                // Copyright
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        '© 2025 Lekky App. All rights reserved.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
