import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../../core/utils/reminder_calculator.dart';
import '../../../../core/services/reminder_content_generator.dart';
import '../../../notifications/data/notification_service.dart';
import '../../../notifications/domain/models/notification.dart';
import '../../../notifications/domain/repositories/notification_repository.dart';
import '../../domain/models/reminder_state.dart';
import '../../domain/models/reminder_event.dart';
import '../../domain/models/reminder_error.dart';

/// Reactive reminder provider using Riverpod best practices
final reminderProvider = AsyncNotifierProvider<ReminderNotifier, ReminderState>(
  ReminderNotifier.new,
);

/// Reminder notifier for reactive state management
class ReminderNotifier extends AsyncNotifier<ReminderState> {
  @override
  Future<ReminderState> build() async {
    return await _loadInitialState();
  }

  /// Load initial reminder state from preferences
  Future<ReminderState> _loadInitialState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final enabled = prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      final frequency =
          prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly';
      final startDateTimeString =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      DateTime? startDateTime;
      if (startDateTimeString != null) {
        try {
          startDateTime = DateTime.parse(startDateTimeString);
        } catch (e) {
          Logger.error('Error parsing reminder start date: $e');
        }
      }

      // Calculate next reminder if enabled and configured
      DateTime? nextReminderDate;
      if (enabled && startDateTime != null) {
        final reminderDates = ReminderCalculator.calculateNextReminders(
          startDateTime: startDateTime,
          frequency: frequency,
          count: 1,
        );
        nextReminderDate =
            reminderDates.isNotEmpty ? reminderDates.first : null;
      }

      return ReminderState(
        enabled: enabled,
        frequency: frequency,
        startDateTime: startDateTime,
        nextReminderDate: nextReminderDate,
        status: enabled && nextReminderDate != null
            ? ReminderStatus.scheduled
            : ReminderStatus.idle,
      );
    } catch (e) {
      Logger.error('Error loading initial reminder state: $e');
      return ReminderState.initial().copyWith(
        error: ReminderError.unknown(details: e.toString()),
        status: ReminderStatus.error,
      );
    }
  }

  /// Update reminder settings and trigger rescheduling
  Future<void> updateSettings({
    bool? enabled,
    String? frequency,
    DateTime? startDateTime,
  }) async {
    final currentState = state.value ?? ReminderState.initial();

    try {
      // Track what changed for event logging
      final changes = <String, dynamic>{};
      if (enabled != null && enabled != currentState.enabled) {
        changes['enabled'] = enabled;
      }
      if (frequency != null && frequency != currentState.frequency) {
        changes['frequency'] = frequency;
      }
      if (startDateTime != currentState.startDateTime) {
        changes['startDateTime'] = startDateTime?.toIso8601String();
      }

      if (changes.isEmpty) return;

      // Update state to show processing
      state = AsyncValue.data(currentState.copyWith(
        status: ReminderStatus.scheduling,
      ));

      // Create new state with updated settings
      final newState = currentState.copyWith(
        enabled: enabled ?? currentState.enabled,
        frequency: frequency ?? currentState.frequency,
        startDateTime: startDateTime ?? currentState.startDateTime,
        error: null, // Clear any previous errors
      );

      // Add settings changed event
      final updatedState = newState.addEvent(
        ReminderEvent.settingsChanged(changes: changes),
      );

      // Schedule reminder if enabled
      if (updatedState.enabled && updatedState.startDateTime != null) {
        await _scheduleReminder(updatedState);
      } else {
        await _cancelReminder(updatedState);
      }
    } catch (e) {
      Logger.error('Error updating reminder settings: $e');
      final errorState = currentState.copyWith(
        status: ReminderStatus.error,
        error: ReminderError.unknown(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        ReminderEvent.error(errorMessage: 'Failed to update settings'),
      ));
    }
  }

  /// Schedule a reminder
  Future<void> _scheduleReminder(ReminderState currentState) async {
    try {
      // Calculate next reminder date
      final reminderDates = ReminderCalculator.calculateNextReminders(
        startDateTime: currentState.startDateTime!,
        frequency: currentState.frequency,
        count: 1,
      );

      if (reminderDates.isEmpty) {
        throw Exception('No valid reminder dates calculated');
      }

      final nextReminderDate = reminderDates.first;

      // Cancel any existing reminder first
      await _cancelExistingReminder();

      // Create notification
      final contentGenerator = ReminderContentGenerator();
      final content = await contentGenerator.generateContextualContent(
        currentState.frequency,
      );

      final notification = AppNotification(
        title: content['title']!,
        message: content['message']!,
        timestamp: nextReminderDate,
        type: NotificationType.readingReminder,
      );

      // Schedule notification
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      await notificationService.scheduleNotification(
          notification, nextReminderDate);

      // Update state with successful scheduling
      final scheduledState = currentState.copyWith(
        status: ReminderStatus.scheduled,
        nextReminderDate: nextReminderDate,
        scheduledReminderId: notification.id,
        lastSuccessfulSchedule: DateTime.now(),
        attemptCount: 0,
      );

      state = AsyncValue.data(scheduledState.addEvent(
        ReminderEvent.scheduled(
          reminderDate: nextReminderDate,
          frequency: currentState.frequency,
        ),
      ));

      Logger.info('Reminder scheduled successfully for $nextReminderDate');
    } catch (e) {
      Logger.error('Error scheduling reminder: $e');
      final errorState = currentState.copyWith(
        status: ReminderStatus.error,
        error: _mapErrorToReminderError(e),
        attemptCount: currentState.attemptCount + 1,
      );

      state = AsyncValue.data(errorState.addEvent(
        ReminderEvent.error(
          errorMessage: 'Failed to schedule reminder',
          errorDetails: e.toString(),
        ),
      ));
    }
  }

  /// Cancel current reminder
  Future<void> _cancelReminder(ReminderState currentState) async {
    try {
      await _cancelExistingReminder();

      final cancelledState = currentState.copyWith(
        status: ReminderStatus.cancelled,
        nextReminderDate: null,
        scheduledReminderId: null,
      );

      state = AsyncValue.data(cancelledState.addEvent(
        ReminderEvent.cancelled(reason: 'Settings disabled'),
      ));

      Logger.info('Reminder cancelled successfully');
    } catch (e) {
      Logger.error('Error cancelling reminder: $e');
    }
  }

  /// Cancel existing scheduled reminder
  Future<void> _cancelExistingReminder() async {
    final currentState = state.value;
    if (currentState?.scheduledReminderId != null) {
      try {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();
        await notificationService
            .cancelNotification(currentState!.scheduledReminderId!);
      } catch (e) {
        Logger.error('Error cancelling existing reminder: $e');
      }
    }
  }

  /// Handle reminder firing and auto-reschedule
  Future<void> onReminderFired() async {
    final currentState = state.value ?? ReminderState.initial();

    try {
      Logger.info('Reminder fired, processing auto-reschedule');

      // Update state to show fired status
      state = AsyncValue.data(currentState
          .copyWith(
            status: ReminderStatus.fired,
          )
          .addEvent(ReminderEvent.fired(
            reminderDate: currentState.nextReminderDate ?? DateTime.now(),
          )));

      // Auto-dismiss from app notifications
      await _dismissCurrentReminder();

      // Auto-reschedule if enabled
      if (currentState.shouldReschedule) {
        await _autoReschedule();
      }
    } catch (e) {
      Logger.error('Error handling reminder firing: $e');
      final errorState = currentState.copyWith(
        status: ReminderStatus.error,
        error: ReminderError.unknown(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        ReminderEvent.error(errorMessage: 'Failed to handle fired reminder'),
      ));
    }
  }

  /// Auto-reschedule next reminder
  Future<void> _autoReschedule() async {
    final currentState = state.value ?? ReminderState.initial();

    try {
      // Update state to show rescheduling
      state = AsyncValue.data(currentState.copyWith(
        status: ReminderStatus.rescheduling,
      ));

      // Schedule next reminder
      await _scheduleReminder(currentState);
    } catch (e) {
      Logger.error('Error auto-rescheduling reminder: $e');
    }
  }

  /// Dismiss current reminder from app notifications
  Future<void> _dismissCurrentReminder() async {
    try {
      final notificationRepository =
          await serviceLocator.getAsync<NotificationRepository>();
      await notificationRepository
          .deleteNotificationsByType(NotificationType.readingReminder);
      Logger.info('Dismissed current reminder from app notifications');
    } catch (e) {
      Logger.error('Failed to dismiss current reminder: $e');
    }
  }

  /// Map generic errors to ReminderError types
  ReminderError _mapErrorToReminderError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('permission')) {
      return ReminderError.permissionDenied(details: error.toString());
    } else if (errorString.contains('platform')) {
      return ReminderError.platformException(details: error.toString());
    } else if (errorString.contains('service')) {
      return ReminderError.serviceUnavailable();
    } else {
      return ReminderError.unknown(details: error.toString());
    }
  }

  /// Clear current error
  void clearError() {
    final currentState = state.value;
    if (currentState != null && currentState.hasError) {
      state = AsyncValue.data(currentState.clearError());
    }
  }

  /// Retry failed operation
  Future<void> retry() async {
    final currentState = state.value;
    if (currentState != null && currentState.hasError) {
      await updateSettings(); // Retry with current settings
    }
  }
}
