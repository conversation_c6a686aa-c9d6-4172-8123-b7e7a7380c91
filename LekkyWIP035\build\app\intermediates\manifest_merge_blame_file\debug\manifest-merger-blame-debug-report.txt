1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\LekkyWIP035\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\LekkyWIP035\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for boot completed to reschedule notifications -->
17-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for background work -->
18-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:6:5-80
18-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:8:5-67
19-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:8:22-65
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- MediaStore permissions for file export -->
20-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:9:5-76
20-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:9:22-74
21    <uses-permission
21-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:12:5-14:38
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:13:9-64
23        android:maxSdkVersion="32" />
23-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:14:9-35
24    <uses-permission
24-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:15:5-17:38
25        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
25-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:16:9-65
26        android:maxSdkVersion="29" />
26-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:17:9-35
27    <!--
28 Required to query activities that can process text, see:
29         https://developer.android.com/training/package-visibility?hl=en and
30         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
31
32         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
33    -->
34    <queries>
34-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:74:5-91:15
35        <intent>
35-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:75:9-78:18
36            <action android:name="android.intent.action.PROCESS_TEXT" />
36-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:76:13-72
36-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:76:21-70
37
38            <data android:mimeType="text/plain" />
38-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:13-50
38-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:19-48
39        </intent>
40        <!-- For document selection -->
41        <intent>
41-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:80:9-82:18
42            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
42-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:81:13-79
42-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:81:21-76
43        </intent>
44        <intent>
44-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:83:9-86:18
45            <action android:name="android.intent.action.CREATE_DOCUMENT" />
45-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:84:13-76
45-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:84:21-73
46
47            <data android:mimeType="text/csv" />
47-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:13-50
47-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:19-48
48        </intent>
49        <intent>
49-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:87:9-90:18
50            <action android:name="android.intent.action.OPEN_DOCUMENT" />
50-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:88:13-74
50-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:88:21-71
51
52            <data android:mimeType="text/csv" />
52-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:13-50
52-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:19-48
53        </intent>
54        <intent>
54-->[:file_picker] D:\000.Workspace\LekkyWIP035\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
55            <action android:name="android.intent.action.GET_CONTENT" />
55-->[:file_picker] D:\000.Workspace\LekkyWIP035\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
55-->[:file_picker] D:\000.Workspace\LekkyWIP035\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
56
57            <data android:mimeType="*/*" />
57-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:13-50
57-->D:\000.Workspace\LekkyWIP035\android\app\src\main\AndroidManifest.xml:77:19-48
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.VIBRATE" />
61-->[:flutter_local_notifications] D:\000.Workspace\LekkyWIP035\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
61-->[:flutter_local_notifications] D:\000.Workspace\LekkyWIP035\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
62    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
62-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
62-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
63
64    <permission
64-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
65        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
65-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
66        android:protectionLevel="signature" />
66-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
67
68    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
69
70    <application
71        android:name="android.app.Application"
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
73        android:debuggable="true"
74        android:enableOnBackInvokedCallback="true"
75        android:icon="@mipmap/ic_launcher"
76        android:label="lekky"
77        android:requestLegacyExternalStorage="true" >
78        <activity
79            android:name="com.lekky.app.MainActivity"
80            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
81            android:exported="true"
82            android:hardwareAccelerated="true"
83            android:launchMode="singleTop"
84            android:theme="@style/LaunchTheme"
85            android:windowSoftInputMode="adjustResize" >
86
87            <!--
88                 Specifies an Android theme to apply to this Activity as soon as
89                 the Android process has started. This theme is visible to the user
90                 while the Flutter UI initializes. After that, this theme continues
91                 to determine the Window background behind the Flutter UI.
92            -->
93            <meta-data
94                android:name="io.flutter.embedding.android.NormalTheme"
95                android:resource="@style/NormalTheme" />
96
97            <intent-filter>
98                <action android:name="android.intent.action.MAIN" />
99
100                <category android:name="android.intent.category.LAUNCHER" />
101            </intent-filter>
102        </activity>
103        <!--
104             Don't delete the meta-data below.
105             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
106        -->
107        <meta-data
108            android:name="flutterEmbedding"
109            android:value="2" />
110
111        <!-- Add receiver for boot completed to reschedule notifications -->
112        <receiver
113            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
114            android:exported="true" >
115            <intent-filter>
116                <action android:name="android.intent.action.BOOT_COMPLETED" />
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
117                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
118            </intent-filter>
119        </receiver>
120
121        <!-- Package name for the application -->
122        <meta-data
123            android:name="com.lekky.app.PACKAGE_NAME"
124            android:value="com.lekky.app" />
125
126        <activity
126-->[:url_launcher_android] D:\000.Workspace\LekkyWIP035\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
127            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
127-->[:url_launcher_android] D:\000.Workspace\LekkyWIP035\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
128            android:exported="false"
128-->[:url_launcher_android] D:\000.Workspace\LekkyWIP035\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
129            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
129-->[:url_launcher_android] D:\000.Workspace\LekkyWIP035\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
130
131        <provider
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
132            android:name="androidx.startup.InitializationProvider"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
133            android:authorities="com.lekky.app.androidx-startup"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
134            android:exported="false" >
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
135            <meta-data
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
136                android:name="androidx.work.WorkManagerInitializer"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
137                android:value="androidx.startup" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
138        </provider>
139
140        <service
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
141            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
143            android:enabled="@bool/enable_system_alarm_service_default"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
144            android:exported="false" />
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
145        <service
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
146            android:name="androidx.work.impl.background.systemjob.SystemJobService"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
148            android:enabled="@bool/enable_system_job_service_default"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
149            android:exported="true"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
150            android:permission="android.permission.BIND_JOB_SERVICE" />
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
151        <service
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
152            android:name="androidx.work.impl.foreground.SystemForegroundService"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
154            android:enabled="@bool/enable_system_foreground_service_default"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
156
157        <receiver
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
158            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
160            android:enabled="true"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
161            android:exported="false" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
162        <receiver
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
163            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
168                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
169                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
178                <action android:name="android.intent.action.BATTERY_OKAY" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
179                <action android:name="android.intent.action.BATTERY_LOW" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
180            </intent-filter>
181        </receiver>
182        <receiver
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
183            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
185            android:enabled="false"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
186            android:exported="false" >
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
187            <intent-filter>
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
188                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
189                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
193            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
198                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
202            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
207                <action android:name="android.intent.action.BOOT_COMPLETED" />
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
208                <action android:name="android.intent.action.TIME_SET" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
209                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
215            android:enabled="@bool/enable_system_alarm_service_default"
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
218                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
222            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
224            android:enabled="true"
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
225            android:exported="true"
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
226            android:permission="android.permission.DUMP" >
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
227            <intent-filter>
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
228                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
229            </intent-filter>
230        </receiver>
231
232        <service
232-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
233            android:name="androidx.room.MultiInstanceInvalidationService"
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
234            android:directBootAware="true"
234-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
235            android:exported="false" />
235-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
236
237        <uses-library
237-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
238            android:name="androidx.window.extensions"
238-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
239            android:required="false" />
239-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
240        <uses-library
240-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
241            android:name="androidx.window.sidecar"
241-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
242            android:required="false" />
242-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
243    </application>
244
245</manifest>
