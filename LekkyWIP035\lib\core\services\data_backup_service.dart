import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/database_constants.dart';
import '../database/database_helper.dart';
import '../utils/logger.dart';
import '../models/meter_entry.dart';
import '../../features/backup/backup_service.dart';

/// Service for handling data backup and restore operations
class DataBackupService {
  final DatabaseHelper _databaseHelper;

  /// Constructor
  DataBackupService(this._databaseHelper);

  /// Create a backup of the database
  Future<String?> createBackup() async {
    try {
      // Get the database file
      final db = await _databaseHelper.database;
      await db.close();

      // Get the application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final dbPath = '${appDocDir.path}/lekky.db';

      // Create a backup directory if it doesn't exist
      final backupDir = Directory('${appDocDir.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Create a backup file with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupPath = '${backupDir.path}/lekky_backup_$timestamp.db';

      // Copy the database file to the backup location
      final dbFile = File(dbPath);
      await dbFile.copy(backupPath);

      // Update the last backup date in shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'last_backup_date', DateTime.now().toIso8601String());

      Logger.info('Backup created at $backupPath');
      return backupPath;
    } catch (e) {
      Logger.error('Failed to create backup: $e');
      return null;
    }
  }

  /// Restore from a backup file
  Future<bool> restoreFromBackup(String backupPath) async {
    try {
      // Close the current database connection
      final db = await _databaseHelper.database;
      await db.close();

      // Get the application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final dbPath = '${appDocDir.path}/lekky.db';

      // Create a backup of the current database before restoring
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final currentBackupPath =
          '${appDocDir.path}/backups/pre_restore_$timestamp.db';

      // Create the backup directory if it doesn't exist
      final backupDir = Directory('${appDocDir.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Backup the current database
      final currentDbFile = File(dbPath);
      if (await currentDbFile.exists()) {
        await currentDbFile.copy(currentBackupPath);
      }

      // Copy the backup file to the database location
      final backupFile = File(backupPath);
      await backupFile.copy(dbPath);

      Logger.info('Database restored from $backupPath');
      return true;
    } catch (e) {
      Logger.error('Failed to restore from backup: $e');
      return false;
    }
  }

  /// Get the last backup date
  Future<DateTime?> getLastBackupDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackupDateStr = prefs.getString('last_backup_date');

      if (lastBackupDateStr != null) {
        return DateTime.parse(lastBackupDateStr);
      }

      return null;
    } catch (e) {
      Logger.error('Failed to get last backup date: $e');
      return null;
    }
  }

  /// Get a list of available backups
  Future<List<String>> getAvailableBackups() async {
    try {
      // Get the application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDocDir.path}/backups');

      if (!await backupDir.exists()) {
        return [];
      }

      // List all backup files
      final files = await backupDir.list().toList();
      final backupFiles = files
          .where((file) =>
              file.path.endsWith('.db') && file.path.contains('lekky_backup_'))
          .map((file) => file.path)
          .toList();

      // Sort by date (newest first)
      backupFiles.sort((a, b) => b.compareTo(a));

      return backupFiles;
    } catch (e) {
      Logger.error('Failed to get available backups: $e');
      return [];
    }
  }

  /// Export data to CSV using BackupService
  Future<String?> exportToCsv() async {
    try {
      final db = await _databaseHelper.database;

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      // Convert to MeterEntry objects
      final entries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        entries.add(MeterEntry(
          id: reading['id'] as int?,
          date: DateTime.parse(reading['date'] as String),
          reading: reading['value'] as double,
          amountToppedUp: 0,
          typeCode: 0,
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp['id'] as int?,
          date: DateTime.parse(topUp['date'] as String),
          reading: 0,
          amountToppedUp: topUp['amount'] as double,
          typeCode: 1,
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Use BackupService to export
      final backupService = BackupService();
      final result = await backupService.exportMeterEntries(entries: entries);

      if (result.isSuccess) {
        Logger.info('Data exported to CSV using BackupService');
        return result.value.path;
      } else {
        Logger.error(
            'Failed to export using BackupService: ${result.error.message}');
        return null;
      }
    } catch (e) {
      Logger.error('Failed to export to CSV: $e');
      return null;
    }
  }

  /// Set auto backup settings
  Future<bool> setAutoBackupSettings({
    required bool enabled,
    required String frequency,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_backup_enabled', enabled);
      await prefs.setString('auto_backup_frequency', frequency);

      Logger.info(
          'Auto backup settings updated: enabled=$enabled, frequency=$frequency');
      return true;
    } catch (e) {
      Logger.error('Failed to set auto backup settings: $e');
      return false;
    }
  }

  /// Get auto backup settings
  Future<Map<String, dynamic>> getAutoBackupSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final enabled = prefs.getBool('auto_backup_enabled') ?? false;
      final frequency = prefs.getString('auto_backup_frequency') ?? 'weekly';

      return {
        'enabled': enabled,
        'frequency': frequency,
      };
    } catch (e) {
      Logger.error('Failed to get auto backup settings: $e');
      return {
        'enabled': false,
        'frequency': 'weekly',
      };
    }
  }
}
