import 'package:flutter/material.dart';

/// A widget for radio button options in settings
class SettingsRadio<T> extends StatelessWidget {
  /// Title of the setting
  final String title;

  /// Options to display
  final List<SettingsRadioOption<T>> options;

  /// Currently selected value
  final T value;

  /// Callback when value changes
  final Function(T) onChanged;

  /// Constructor
  const SettingsRadio({
    super.key,
    required this.title,
    required this.options,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        ...options.map((option) => _buildRadioOption(context, option)),
      ],
    );
  }

  Widget _buildRadioOption(
      BuildContext context, SettingsRadioOption<T> option) {
    return InkWell(
      onTap: () => onChanged(option.value),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Radio<T>(
              value: option.value,
              groupValue: value,
              onChanged: (newValue) {
                if (newValue != null) {
                  onChanged(newValue);
                }
              },
            ),
            const SizedBox(width: 8),
            if (option.icon != null) ...[
              Icon(option.icon),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (option.description != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      option.description!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Class representing a radio option
class SettingsRadioOption<T> {
  /// Value of the option
  final T value;

  /// Title of the option
  final String title;

  /// Description of the option
  final String? description;

  /// Icon to display
  final IconData? icon;

  /// Constructor
  const SettingsRadioOption({
    required this.value,
    required this.title,
    this.description,
    this.icon,
  });
}
