import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../../core/utils/average_calculator.dart';

/// Immutable state for the Dashboard/Home feature
class DashboardState {
  /// Latest meter reading
  final MeterReading? latestMeterReading;

  /// Recent average daily usage
  final double? recentAverageDailyUsage;

  /// Total average daily usage
  final double? totalAverageDailyUsage;

  /// Recent entries (both meter readings and top-ups)
  final List<dynamic> recentEntries;

  /// Total top-ups after latest meter reading
  final double totalTopUpsAfterLatestReading;

  /// Loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Whether this is the first launch
  final bool isFirstLaunch;

  /// Constructor
  const DashboardState({
    this.latestMeterReading,
    this.recentAverageDailyUsage,
    this.totalAverageDailyUsage,
    this.recentEntries = const [],
    this.totalTopUpsAfterLatestReading = 0.0,
    this.isLoading = false,
    this.errorMessage,
    this.isFirstLaunch = false,
  });

  /// Initial dashboard state
  factory DashboardState.initial() => const DashboardState(isLoading: true);

  /// Create a copy with some fields changed
  DashboardState copyWith({
    MeterReading? latestMeterReading,
    double? recentAverageDailyUsage,
    double? totalAverageDailyUsage,
    List<dynamic>? recentEntries,
    double? totalTopUpsAfterLatestReading,
    bool? isLoading,
    String? errorMessage,
    bool? isFirstLaunch,
  }) {
    return DashboardState(
      latestMeterReading: latestMeterReading ?? this.latestMeterReading,
      recentAverageDailyUsage:
          recentAverageDailyUsage ?? this.recentAverageDailyUsage,
      totalAverageDailyUsage:
          totalAverageDailyUsage ?? this.totalAverageDailyUsage,
      recentEntries: recentEntries ?? this.recentEntries,
      totalTopUpsAfterLatestReading:
          totalTopUpsAfterLatestReading ?? this.totalTopUpsAfterLatestReading,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
    );
  }
}

/// Extension methods for DashboardState
extension DashboardStateX on DashboardState {
  /// Check if there's an error
  bool get hasError => errorMessage != null;

  /// Check if there are recent entries
  bool get hasRecentEntries => recentEntries.isNotEmpty;

  /// Check if there's a latest meter reading
  bool get hasLatestMeterReading => latestMeterReading != null;

  /// Check if usage statistics are available
  bool get hasUsageStatistics =>
      recentAverageDailyUsage != null || totalAverageDailyUsage != null;

  /// Calculate days remaining based on current balance and usage, accounting for reading date
  double? calculateDaysRemaining() {
    if (latestMeterReading == null ||
        recentAverageDailyUsage == null ||
        recentAverageDailyUsage! <= 0) {
      return null;
    }

    return AverageCalculator.calculateDaysRemaining(
      lastMeterReading: latestMeterReading!.value,
      topUpsSinceLastReading: totalTopUpsAfterLatestReading,
      lastReadingDate: latestMeterReading!.date,
      averageUsage: recentAverageDailyUsage!,
    );
  }

  /// Get current balance (latest reading + top-ups after)
  double get currentBalance {
    if (latestMeterReading == null) return 0.0;
    return latestMeterReading!.value + totalTopUpsAfterLatestReading;
  }

  /// Calculate days to alert threshold using projected balance
  double? calculateDaysToAlertThreshold(
      double alertThreshold, int daysInAdvance) {
    if (latestMeterReading == null) {
      return null;
    }

    return AverageCalculator.calculateDaysToAlertThreshold(
      lastMeterReading: latestMeterReading!.value,
      topUpsSinceLastReading: totalTopUpsAfterLatestReading,
      lastReadingDate: latestMeterReading!.date,
      alertThreshold: alertThreshold,
      recentAverageUsage: recentAverageDailyUsage,
      totalAverageUsage: totalAverageDailyUsage,
      daysInAdvance: daysInAdvance,
    );
  }

  /// Calculate days to meter zero (wrapper for existing method)
  double? calculateDaysToMeterZero() => calculateDaysRemaining();

  /// Get meter readings from recent entries
  List<MeterReading> get recentMeterReadings =>
      recentEntries.whereType<MeterReading>().toList();

  /// Get top-ups from recent entries
  List<TopUp> get recentTopUps => recentEntries.whereType<TopUp>().toList();

  /// Check if data exists (not first launch)
  bool get hasData => latestMeterReading != null || recentEntries.isNotEmpty;
}
