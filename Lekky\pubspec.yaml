name: lekky
description: "A prepaid electricity meter tracking app."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.3.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core
  intl: ^0.18.1
  cupertino_icons: ^1.0.6

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  freezed_annotation: ^2.4.1

  # Navigation
  go_router: ^5.0.0

  # Database and Storage
  sqflite: ^2.0.0
  path: ^1.8.3
  path_provider: ^2.0.11
  shared_preferences: ^2.0.15

  # Dependency Injection
  get_it: ^7.0.0
  injectable: ^1.0.0

  # UI Components
  flutter_svg: ^1.0.0
  google_fonts: ^3.0.0

  # Data Visualization
  fl_chart: ^0.55.0

  # Notifications
  flutter_local_notifications: ^16.3.0
  timezone: ^0.9.0
  workmanager: ^0.5.2

  # Utilities
  uuid: ^3.0.0
  equatable: ^2.0.0
  logger: ^1.1.0
  permission_handler: ^11.3.1
  file_picker: ^5.2.10
  csv: ^5.0.2
  url_launcher: ^6.1.10
  flutter_paypal: ^0.2.0
  marquee: ^2.2.3
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.0

  # Legacy (to be migrated)
  provider: ^6.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # Testing
  mockito: ^5.0.0

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.0.0
  riverpod_generator: ^2.3.9
  injectable_generator: ^1.0.0
  freezed: ^2.4.7

  # Linting
  flutter_lints: ^3.0.0

  # Development Tools
  flutter_launcher_icons: ^0.10.0
  flutter_native_splash: ^2.0.0

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/icon.png
    - assets/background.png
    - assets/splash.png
    - assets/dark_mode_background.png

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon.png"
  min_sdk_android: 21
