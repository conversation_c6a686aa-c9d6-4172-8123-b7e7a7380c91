import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/permission_helper.dart';
import '../../../core/di/service_locator.dart';

/// Provider for export data state
final exportDataProvider =
    StateNotifierProvider<ExportDataNotifier, ExportDataState>((ref) {
  return ExportDataNotifier();
});

/// Export data state
class ExportDataState {
  final bool isExporting;
  final bool isCompleted;
  final bool isSuccessful;
  final String? errorMessage;
  final String? filePath;

  const ExportDataState({
    this.isExporting = false,
    this.isCompleted = false,
    this.isSuccessful = false,
    this.errorMessage,
    this.filePath,
  });

  ExportDataState copyWith({
    bool? isExporting,
    bool? isCompleted,
    bool? isSuccessful,
    String? errorMessage,
    String? filePath,
  }) {
    return ExportDataState(
      isExporting: isExporting ?? this.isExporting,
      isCompleted: isCompleted ?? this.isCompleted,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
      filePath: filePath ?? this.filePath,
    );
  }
}

/// Export data notifier
class ExportDataNotifier extends StateNotifier<ExportDataState> {
  ExportDataNotifier() : super(const ExportDataState());

  static const String _backupFilename = 'lekky_export_101.csv';
  static const int _backupFormatVersion = 101;

  final PermissionHelper _permissionHelper = PermissionHelper();

  /// Export data using Storage Access Framework with fallbacks for Android 14+ compatibility
  Future<void> exportData() async {
    state = state.copyWith(isExporting: true, isCompleted: false);

    try {
      // Get database helper
      final databaseHelper = serviceLocator<DatabaseHelper>();
      final db = await databaseHelper.database;

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      // Convert to MeterEntry objects
      final entries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        entries.add(MeterEntry(
          id: reading['id'] as int?,
          date: DateTime.parse(reading['date'] as String),
          reading: reading['value'] as double,
          amountToppedUp: 0,
          typeCode: 0,
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp['id'] as int?,
          date: DateTime.parse(topUp['date'] as String),
          reading: 0,
          amountToppedUp: topUp['amount'] as double,
          typeCode: 1,
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$_backupFormatVersion'],
        ['Date', 'Type', 'Amount'],
        ...entries.map((entry) => [
              entry.timestamp.toIso8601String(),
              entry.typeCode.toString(),
              entry.amountToppedUp > 0
                  ? entry.amountToppedUp.toString()
                  : entry.reading.toString(),
            ]),
      ];

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);

      // Try Storage Access Framework first (recommended for Android 10+)
      String? filePath = await _tryStorageAccessFramework(csv);

      // If SAF failed, try traditional method with permissions
      if (filePath == null) {
        filePath = await _tryTraditionalMethod(csv);
      }

      // If both methods failed, create temp file for sharing
      if (filePath == null) {
        filePath = await _createTempFile(csv);
        state = state.copyWith(
          isExporting: false,
          isCompleted: true,
          isSuccessful: false,
          errorMessage:
              'Could not save to Downloads folder. Please use the Share option to save your backup.',
          filePath: filePath,
        );
        return;
      }

      Logger.info('Data exported successfully to: $filePath');
      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: true,
        filePath: filePath,
      );
    } catch (e) {
      Logger.error('Failed to export data: $e');

      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Try using Storage Access Framework to save the file
  Future<String?> _tryStorageAccessFramework(String csv) async {
    try {
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Backup File',
        fileName: _backupFilename,
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsString(csv);
        Logger.info('Data exported using Storage Access Framework: $result');
        return result;
      }
    } catch (e) {
      Logger.warning('Storage Access Framework failed: $e');
    }
    return null;
  }

  /// Try traditional method with permissions
  Future<String?> _tryTraditionalMethod(String csv) async {
    try {
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();
      if (hasPermission) {
        final downloadsPath = await _getDownloadsPath();
        if (downloadsPath != null) {
          final path = '$downloadsPath/$_backupFilename';
          final file = File(path);
          await file.writeAsString(csv);
          Logger.info('Data exported using traditional method: $path');
          return path;
        }
      }
    } catch (e) {
      Logger.warning('Traditional method failed: $e');
    }
    return null;
  }

  /// Create temporary file as fallback
  Future<String> _createTempFile(String csv) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/$_backupFilename');
    await tempFile.writeAsString(csv);
    return tempFile.path;
  }

  /// Get Downloads folder path for traditional method
  Future<String?> _getDownloadsPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          final externalDir = directory.path.split('/Android')[0];
          final downloadsDir = '$externalDir/Download';

          final dir = Directory(downloadsDir);
          if (!await dir.exists()) {
            await dir.create(recursive: true);
          }

          return downloadsDir;
        }
      }

      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } catch (e) {
      Logger.error('Error getting downloads path: $e');
      return null;
    }
  }

  /// Reset the export state
  void resetState() {
    state = const ExportDataState();
  }
}
