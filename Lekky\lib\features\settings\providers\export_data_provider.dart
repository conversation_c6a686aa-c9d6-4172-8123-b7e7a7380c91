import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/permission_helper.dart';
import '../../../core/di/service_locator.dart';

/// Export result class for better error handling
class ExportResult {
  final bool isSuccess;
  final String? filePath;
  final String? errorMessage;

  const ExportResult({
    required this.isSuccess,
    this.filePath,
    this.errorMessage,
  });

  factory ExportResult.success(String filePath) {
    return ExportResult(isSuccess: true, filePath: filePath);
  }

  factory ExportResult.failure(String errorMessage, {String? tempFilePath}) {
    return ExportResult(
      isSuccess: false,
      errorMessage: errorMessage,
      filePath: tempFilePath,
    );
  }
}

/// Provider for export data state
final exportDataProvider =
    StateNotifierProvider<ExportDataNotifier, ExportDataState>((ref) {
  return ExportDataNotifier();
});

/// Export data state
class ExportDataState {
  final bool isExporting;
  final bool isCompleted;
  final bool isSuccessful;
  final String? errorMessage;
  final String? filePath;

  const ExportDataState({
    this.isExporting = false,
    this.isCompleted = false,
    this.isSuccessful = false,
    this.errorMessage,
    this.filePath,
  });

  ExportDataState copyWith({
    bool? isExporting,
    bool? isCompleted,
    bool? isSuccessful,
    String? errorMessage,
    String? filePath,
  }) {
    return ExportDataState(
      isExporting: isExporting ?? this.isExporting,
      isCompleted: isCompleted ?? this.isCompleted,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
      filePath: filePath ?? this.filePath,
    );
  }
}

/// Export data notifier
class ExportDataNotifier extends StateNotifier<ExportDataState> {
  ExportDataNotifier() : super(const ExportDataState());

  static const String _backupFilename = 'lekky_export_101.csv';
  static const int _backupFormatVersion = 101;

  final PermissionHelper _permissionHelper = PermissionHelper();

  /// Export data with Android version-aware strategy for maximum compatibility
  Future<void> exportData() async {
    state = state.copyWith(isExporting: true, isCompleted: false);

    try {
      Logger.info('Starting CSV export process');

      // Get database helper
      final databaseHelper = serviceLocator<DatabaseHelper>();
      final db = await databaseHelper.database;

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      Logger.info(
          'Retrieved ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      // Convert to MeterEntry objects
      final entries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        entries.add(MeterEntry(
          id: reading['id'] as int?,
          date: DateTime.parse(reading['date'] as String),
          reading: reading['value'] as double,
          amountToppedUp: 0,
          typeCode: 0,
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp['id'] as int?,
          date: DateTime.parse(topUp['date'] as String),
          reading: 0,
          amountToppedUp: topUp['amount'] as double,
          typeCode: 1,
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$_backupFormatVersion'],
        ['Date', 'Type', 'Amount'],
        ...entries.map((entry) => [
              entry.timestamp.toIso8601String(),
              entry.typeCode.toString(),
              entry.amountToppedUp > 0
                  ? entry.amountToppedUp.toString()
                  : entry.reading.toString(),
            ]),
      ];

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);

      // Determine export strategy based on platform and Android version
      final exportResult = await _executeVersionAwareExport(csv);

      if (exportResult.isSuccess) {
        Logger.info('Data exported successfully to: ${exportResult.filePath}');
        state = state.copyWith(
          isExporting: false,
          isCompleted: true,
          isSuccessful: true,
          filePath: exportResult.filePath,
        );
      } else {
        Logger.warning('Export failed: ${exportResult.errorMessage}');
        state = state.copyWith(
          isExporting: false,
          isCompleted: true,
          isSuccessful: false,
          errorMessage: exportResult.errorMessage,
          filePath: exportResult.filePath,
        );
      }
    } catch (e) {
      Logger.error('Failed to export data: $e');

      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Execute version-aware export strategy based on platform and Android version
  Future<ExportResult> _executeVersionAwareExport(String csv) async {
    try {
      if (Platform.isIOS) {
        Logger.info('iOS detected - using Storage Access Framework');
        final filePath = await _tryStorageAccessFramework(csv);
        if (filePath != null) {
          return ExportResult.success(filePath);
        }
        return ExportResult.failure(
            'Could not save file on iOS. Please try again.');
      }

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;
        Logger.info('Android API level detected: $sdkInt');

        // For Android 10+ (API 29+): Prioritize Downloads folder with SAF fallback
        if (sdkInt >= 29) {
          Logger.info('Android 10+ detected - trying Downloads folder first');

          // Try traditional Downloads folder access first
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to SAF for user-controlled location
          Logger.info(
              'Downloads folder failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning(
              'Both methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage. You can share it from the notification or find it in your file manager under Android/data/com.lekky.app/files/',
            tempFilePath: tempPath,
          );
        }

        // For Android 8.0.0 and older (API 26 and below): Prioritize traditional method
        else if (sdkInt >= 21) {
          Logger.info(
              'Android 5.0-9.0 detected - using traditional file access');

          // Try traditional method first (more reliable on older Android)
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Try SAF as fallback (limited support on older versions)
          Logger.info(
              'Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning(
              'Both methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder due to Android version limitations. File saved to app storage. Please use the Share option to save your backup to your preferred location.',
            tempFilePath: tempPath,
          );
        }

        // For very old Android versions (below API 21)
        else {
          Logger.warning(
              'Very old Android version detected (API $sdkInt) - limited export options');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Your Android version has limited file access. File saved to app storage. Please use the Share option to save your backup.',
            tempFilePath: tempPath,
          );
        }
      }

      // Fallback for unknown platforms
      Logger.warning('Unknown platform detected - using fallback method');
      final tempPath = await _createTempFile(csv);
      return ExportResult.failure(
        'Platform not fully supported. File saved to temporary storage. Please use the Share option.',
        tempFilePath: tempPath,
      );
    } catch (e) {
      Logger.error('Error in version-aware export: $e');
      try {
        final tempPath = await _createTempFile(csv);
        return ExportResult.failure(
          'Export error occurred. File saved to temporary storage. Error: ${e.toString()}',
          tempFilePath: tempPath,
        );
      } catch (tempError) {
        Logger.error('Failed to create temp file: $tempError');
        return ExportResult.failure(
            'Export completely failed: ${e.toString()}');
      }
    }
  }

  /// Try using Storage Access Framework to save the file
  Future<String?> _tryStorageAccessFramework(String csv) async {
    try {
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Backup File',
        fileName: _backupFilename,
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsString(csv);
        Logger.info('Data exported using Storage Access Framework: $result');
        return result;
      }
    } catch (e) {
      Logger.warning('Storage Access Framework failed: $e');
    }
    return null;
  }

  /// Try traditional method with permissions
  Future<String?> _tryTraditionalMethod(String csv) async {
    try {
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();
      if (hasPermission) {
        final downloadsPath = await _getDownloadsPath();
        if (downloadsPath != null) {
          final path = '$downloadsPath/$_backupFilename';
          final file = File(path);
          await file.writeAsString(csv);
          Logger.info('Data exported using traditional method: $path');
          return path;
        }
      }
    } catch (e) {
      Logger.warning('Traditional method failed: $e');
    }
    return null;
  }

  /// Create temporary file as fallback
  Future<String> _createTempFile(String csv) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/$_backupFilename');
    await tempFile.writeAsString(csv);
    return tempFile.path;
  }

  /// Get Downloads folder path for traditional method with fallback to app-specific storage
  Future<String?> _getDownloadsPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          // Try to access public Downloads folder
          try {
            final externalDir = directory.path.split('/Android')[0];
            final downloadsDir = '$externalDir/Download';

            final dir = Directory(downloadsDir);
            if (!await dir.exists()) {
              await dir.create(recursive: true);
            }

            // Test write access
            final testFile = File('$downloadsDir/.lekky_test');
            await testFile.writeAsString('test');
            await testFile.delete();

            Logger.info('Downloads folder access confirmed: $downloadsDir');
            return downloadsDir;
          } catch (e) {
            Logger.warning('Cannot access public Downloads folder: $e');

            // Fallback to app-specific external storage
            final appExternalDir = directory.path;
            final dir = Directory(appExternalDir);
            if (!await dir.exists()) {
              await dir.create(recursive: true);
            }

            Logger.info('Using app-specific external storage: $appExternalDir');
            return appExternalDir;
          }
        }
      }

      // iOS fallback
      final directory = await getApplicationDocumentsDirectory();
      Logger.info('Using iOS documents directory: ${directory.path}');
      return directory.path;
    } catch (e) {
      Logger.error('Error getting downloads path: $e');
      return null;
    }
  }

  /// Reset the export state
  void resetState() {
    state = const ExportDataState();
  }
}
