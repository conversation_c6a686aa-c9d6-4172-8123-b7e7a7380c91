import '../../../../core/utils/date_time_utils.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';

class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._perReadingAverageRepository,
  );

  Future<double> calculateCustomPeriodCost(
      DateTime fromDate, DateTime toDate) async {
    final allReadings = await _meterReadingRepository.getAllMeterReadings();
    final allTopUps = await _topUpRepository.getAllTopUps();

    if (allReadings.isEmpty) return 0.0;

    allReadings.sort((a, b) => a.date.compareTo(b.date));

    // Find readings within the period
    final readingsInPeriod = allReadings
        .where((reading) =>
            reading.date.isAfter(fromDate) && reading.date.isBefore(toDate))
        .toList();

    if (readingsInPeriod.isEmpty) {
      // No readings in period - use next available recent average
      final nextReading = allReadings.firstWhere(
        (reading) => reading.date.isAfter(toDate),
        orElse: () => allReadings.last,
      );
      final recentAverage = await _perReadingAverageRepository
          .getPerReadingAverageByMeterReadingId(nextReading.id!);
      if (recentAverage == null) return 0.0;

      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return recentAverage.recentAveragePerDay * days;
    }

    if (readingsInPeriod.length < 2) {
      // Simplified calculation: start + end intervals only
      return await _calculateSimplifiedCost(
          fromDate, toDate, readingsInPeriod.first, allReadings);
    }

    // Complex calculation: start + middle + end intervals
    return await _calculateComplexCost(
        fromDate, toDate, readingsInPeriod, allReadings, allTopUps);
  }

  Future<double> _calculateSimplifiedCost(
      DateTime fromDate, DateTime toDate, reading, List allReadings) async {
    double totalCost = 0.0;

    // Start interval: fromDate to reading time
    final startAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(reading.id!);
    if (startAverage != null) {
      final startDays =
          DateTimeUtils.calculateDaysWithPrecision(fromDate, reading.date);
      totalCost += startAverage.recentAveragePerDay * startDays;
    }

    // End interval: reading time to toDate
    final nextReading = allReadings.firstWhere(
      (r) => r.date.isAfter(toDate),
      orElse: () => allReadings.last,
    );
    final endAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(nextReading.id!);
    if (endAverage != null) {
      final endDays =
          DateTimeUtils.calculateDaysWithPrecision(reading.date, toDate);
      totalCost += endAverage.recentAveragePerDay * endDays;
    }

    return totalCost;
  }

  Future<double> _calculateComplexCost(DateTime fromDate, DateTime toDate,
      List readingsInPeriod, List allReadings, List allTopUps) async {
    double totalCost = 0.0;

    // Start interval
    final firstReading = readingsInPeriod.first;
    final startAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(firstReading.id!);
    if (startAverage != null) {
      final startDays =
          DateTimeUtils.calculateDaysWithPrecision(fromDate, firstReading.date);
      totalCost += startAverage.recentAveragePerDay * startDays;
    }

    // Middle periods - direct meter differences with top-ups
    for (int i = 0; i < readingsInPeriod.length - 1; i++) {
      final currentReading = readingsInPeriod[i];
      final nextReading = readingsInPeriod[i + 1];

      // Find top-ups between these readings
      double topUpsBetween = 0.0;
      for (final topUp in allTopUps) {
        if (topUp.date.isAfter(currentReading.date) &&
            topUp.date.isBefore(nextReading.date)) {
          topUpsBetween += topUp.amount;
        }
      }

      // Calculate usage: previous_reading - current_reading + top_ups
      final usage = (currentReading.value - nextReading.value) + topUpsBetween;
      totalCost += usage;
    }

    // End interval
    final lastReading = readingsInPeriod.last;
    final nextReading = allReadings.firstWhere(
      (r) => r.date.isAfter(toDate),
      orElse: () => allReadings.last,
    );
    final endAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(nextReading.id!);
    if (endAverage != null) {
      final endDays =
          DateTimeUtils.calculateDaysWithPrecision(lastReading.date, toDate);
      totalCost += endAverage.recentAveragePerDay * endDays;
    }

    return totalCost;
  }
}
