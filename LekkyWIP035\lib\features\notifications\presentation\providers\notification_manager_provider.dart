import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/services/smart_notification_service.dart';
import 'notification_evaluator_provider.dart';
import 'notification_provider.dart';
import '../../domain/models/notification.dart';

part 'notification_manager_provider.g.dart';

/// Provider that manages notification firing and coordination
@riverpod
class NotificationManager extends _$NotificationManager {
  @override
  Future<void> build() async {
    // Listen to notification evaluator for reactive updates
    ref.listen(notificationEvaluatorProvider, (previous, next) {
      next.whenData((notifications) {
        if (notifications.isNotEmpty) {
          _processNotifications(notifications);
        }
      });
    });
  }

  /// Process and fire notifications through smart notification service
  Future<void> _processNotifications(
      List<AppNotification> notifications) async {
    try {
      final smartService = SmartNotificationService();
      for (final notification in notifications) {
        // Check if notification should be fired (deduplication, etc.)
        final shouldFire =
            await smartService.shouldFireNotification(notification);

        if (shouldFire) {
          // Create notification through notification service
          final notificationService = ref.read(notificationServiceProvider);
          await notificationService.showNotification(notification);

          // Invalidate notification provider for immediate UI update
          ref.invalidate(notificationProvider);

          // Record notification for deduplication
          await smartService.recordNotificationFired(notification);

          Logger.info(
              'NotificationManager: Created and fired ${notification.type} notification');
        }
      }
    } catch (e) {
      Logger.error('NotificationManager: Error processing notifications: $e');
    }
  }

  /// Manually trigger notification evaluation
  Future<void> checkAndFireNotifications() async {
    try {
      ref.invalidate(notificationEvaluatorProvider);
    } catch (e) {
      Logger.error('NotificationManager: Error triggering evaluation: $e');
    }
  }

  /// Clear all notification data
  Future<void> clearAllNotificationData() async {
    try {
      final smartService = SmartNotificationService();
      await smartService.clearAllData();
      Logger.info('NotificationManager: Cleared all notification data');
    } catch (e) {
      Logger.error('NotificationManager: Error clearing data: $e');
    }
  }
}
