import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Time Display settings screen
class TimeDisplayScreen extends ConsumerWidget {
  /// Constructor
  const TimeDisplayScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Banner with back arrow
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: AppBanner(
                      message: '← Time Display',
                      gradientColors: AppColors.getSettingsMainCardGradient(
                          Theme.of(context).brightness == Brightness.dark),
                      textColor: AppColors.getAppBarTextColor('settings',
                          Theme.of(context).brightness == Brightness.dark),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Time display card
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SettingsSectionHeader(
                                    title: 'Time Display',
                                    description:
                                        'Choose whether to show time alongside dates.',
                                    icon: Icons.access_time,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Show time with date',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    'When enabled, times will be shown alongside dates throughout the app.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Show time with date',
                                        style: TextStyle(fontSize: 16),
                                      ),
                                      Switch(
                                        value: settings.showTimeWithDate,
                                        onChanged: (value) {
                                          ref
                                              .read(settingsProvider.notifier)
                                              .updateShowTimeWithDate(value);
                                        },
                                        activeColor: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    settings.showTimeWithDate
                                        ? 'Example: 11-05-2025 16:39'
                                        : 'Example: 11-05-2025',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
