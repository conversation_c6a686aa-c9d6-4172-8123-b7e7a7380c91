import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/date_formatter_provider.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';

/// A card that displays recent activity (meter readings and top-ups)
class RecentActivityCard extends ConsumerWidget {
  /// List of recent entries (both meter readings and top-ups)
  final List<dynamic> recentEntries;

  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when the View All button is pressed
  final VoidCallback onViewAll;

  /// Constructor
  const RecentActivityCard({
    super.key,
    required this.recentEntries,
    this.currencySymbol = '₦',
    required this.onViewAll,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Activity',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: onViewAll,
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (recentEntries.isEmpty)
              _buildEmptyState(context)
            else
              ...recentEntries
                  .map((entry) => _buildEntryItem(context, ref, entry))
                  .toList(),
          ],
        ),
      ),
    );
  }

  /// Build an empty state when there are no entries
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24),
      alignment: Alignment.center,
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No recent activity',
            style: TextStyle(
              fontSize: 16,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first meter reading or top-up',
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build a single entry item
  Widget _buildEntryItem(BuildContext context, WidgetRef ref, dynamic entry) {
    final theme = Theme.of(context);
    final bool isMeterReading = entry is MeterReading;

    // Determine icon and color based on entry type
    IconData icon;
    Color iconColor;
    String title;
    String value;

    if (isMeterReading) {
      icon = Icons.speed;
      iconColor = theme.colorScheme.primary;
      title = 'Meter Reading';
      value =
          '$currencySymbol${(entry as MeterReading).value.toStringAsFixed(2)}';
    } else {
      icon = Icons.add_card;
      iconColor = theme.colorScheme.secondary;
      title = 'Top-up';
      value = '$currencySymbol${(entry as TopUp).amount.toStringAsFixed(2)}';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  ref.watch(dateFormatterProvider).formatDateForRecentActivity(
                        isMeterReading
                            ? (entry as MeterReading).date
                            : (entry as TopUp).date,
                      ),
                  style: TextStyle(
                    fontSize: 14,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
