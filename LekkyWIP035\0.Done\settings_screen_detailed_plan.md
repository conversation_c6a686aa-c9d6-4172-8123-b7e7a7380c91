# NewLekky Settings Screen Detailed Plan

This document provides comprehensive specifications for the Settings screen in the NewLekky app, based on the screenshots provided.

## 1. Visual Design

### Header
- **Background Color**: Dark gray (`#424242`)
- **Title**: "Settings" in white text, left-aligned
- **Back Button**: White back arrow icon, right-aligned

### Message Banner
- **Background Color**: Light gray/off-white
- **Text**: Rotating helpful messages that change based on the current settings section
- **Examples**:
  - "Update thresholds when your plan changes."
  - "Export history for spreadsheet analysis."
  - "Review your usage history to find savings."
  - "Date column shows when readings/top-ups happened."
  - "Total-avg shows usage since your first reading."
  - "Compare monthly averages to spot patterns."
  - "Check the app daily for better control."
  - "Test scenarios to see potential savings."

### Settings Cards
- **Background Color**: Light gray with rounded corners
- **Toggle Switches**: Blue when active, purple/gray when inactive
- **Text**: Dark gray for titles, lighter gray for descriptions/current values
- **Icons**: Blue icons for category headers

## 2. Settings Structure

### Main Categories
1. **Language**
   - Globe icon
   - Language selection options

2. **Currency**
   - Money icon
   - Currency selection options

3. **Alerts & Notifications**
   - Bell icon
   - Collapsible section containing Alert Threshold, Days in Advance, Notifications, and Reminders

4. **Date Settings**
   - Calendar icon
   - Date format and display configuration

5. **Appearance**
   - Palette icon
   - Theme selection options

6. **Data Backup**
   - List icon
   - Export, import, and data management options

7. **About**
   - Info icon
   - App version, upgrade options, and tips

8. **Donate**
   - Heart icon
   - Support options for the app

9. **For Testing Only** (development mode)
   - Bug icon
   - Developer testing options

10. **Cost Analysis**
    - Chart icon
    - Cost per Day/Week/Month/Year options

### Subcategories and Settings

#### Language Settings
1. **Language Selection**
   - Toggle to enable/disable
   - Radio button list with language options:
     - English (default)
     - Spanish
     - French
     - German
     - Italian
     - Portuguese
     - Russian
     - Chinese
     - Japanese

#### Currency Settings
1. **Currency Selection**
   - Toggle to enable/disable
   - Radio button list with currency options:
     - $ (USD)
     - € (EUR)
     - CN¥ (CNY)
     - ₹ (INR)
     - ¥ (JPY)
     - £ (GBP) (default)
     - R$ (BRL)
     - ₽ (RUB)
     - Rp (IDR)
     - C$ (CAD)
     - A$ (AUD)
     - Mex$ (MXN)

#### Alerts & Notifications Settings
1. **Alerts & Notifications**
   - Toggle switch to enable/disable
   - When enabled, shows notification options
   - Description: "Get alerts for low balance and usage patterns"

2. **Alert Threshold**
   - Toggle switch to enable/disable
   - When enabled, shows input field for threshold value
   - Description: "Get notified when balance falls below this amount"
   - Default: £5.00
   - Validation: Must be between £1.00 and £999.00

3. **Days in Advance**
   - Toggle switch to enable/disable
   - When enabled, shows input field for days value
   - Description: "Get notified this many days before you run out"
   - Default: 2 days
   - Validation: Must be between 1 and 99 days

4. **Notifications**
   - Toggle switch to enable/disable
   - When enabled, shows notification options
   - Description: "Configure notification preferences"
   - Sub-options:
     - **Enable Notifications**: Master toggle for all notifications
     - **Enable Low Balance Alerts**: Alerts when balance falls below threshold
     - **Enable Time to Top Up Alerts**: Alerts based on usage patterns
     - **Enable Time To No Units Alerts**: Alerts when units are projected to run out
     - **Enable Invalid Record Alerts**: Alerts for suspicious or invalid entries

5. **Reminders**
   - Toggle switch to enable/disable
   - When enabled, shows reminder options
   - Description: "Set up reminders to check your meter"
   - Sub-options:
     - **Enable Meter Reminders**: Master toggle for reminders
     - **Reminder Frequency**: Daily/Weekly/Bi-weekly/Monthly
     - **Reminder Time**: Time of day to receive reminders
     - **Next Reminder Date**: Shows the date of the next scheduled reminder
     - **Last Reminder Time**: Shows when the last reminder was sent

#### Date Settings
1. **Date Format**
   - Toggle switch to enable/disable
   - Current selection display: "Current: DD-MM-YYYY"
   - When enabled, shows radio button list of date formats:
     - DD-MM-YYYY (11-05-2025) - default
     - MM-DD-YYYY (05-11-2025)
     - YYYY-MM-DD (2025-05-11)
   - Helper text: "Choose how dates will be displayed"

2. **Date Info**
   - Toggle switch to enable/disable
   - Current selection display: "Current: Date and time"
   - When enabled, shows radio button list of options:
     - Date only (Example: 11-05-2025)
     - Date and time (Example: 11-05-2025 16:39) - default
   - Helper text: "Show date only or date and time"

#### Appearance Settings
1. **Appearance**
   - Toggle switch to enable/disable
   - When enabled, shows theme selection options:
     - System Default (follows device theme)
     - Light Mode
     - Dark Mode
   - Helper text: "Choose light or dark theme"

#### Data Backup Settings
1. **Data Backup**
   - Toggle switch to enable/disable
   - When enabled, shows data management options:
     - **Export Data**: Option to export data to CSV file
       - Description: "Export your data to a CSV file"
     - **Import Data**: Option to import data from CSV file
       - Description: "Import data from a CSV file"
     - **Clear All Data**: Option to delete all data
       - Description: "Delete all your data"
       - Warning: "This action cannot be undone"

#### About Settings
1. **About**
   - Toggle switch to enable/disable
   - When enabled, shows app information:
     - **Version**: Current app version (e.g., "1.0.1 beta")
     - **Upgrade**: Premium features option
     - **Tips & Tricks**: Learn how to get the most out of Lekky

#### Donate Settings
1. **Donate**
   - Toggle switch to enable/disable
   - When enabled, shows donation options
   - Description: "Support the development of Lekky"

#### Cost Analysis Settings
1. **Cost Analysis**
   - Toggle switch to enable/disable
   - When enabled, shows cost analysis options
   - Description: "Configure cost analysis settings"
   - Sub-options:
     - **Cost per Day**: View daily cost averages
     - **Cost per Week**: View weekly cost averages
     - **Cost per Month**: View monthly cost averages
     - **Cost per Year**: View yearly cost averages
     - **Cost per Custom Range**: Set custom date range for cost analysis

#### For Testing Only
1. **For Testing Only**
   - Toggle switch to enable/disable
   - Only visible in development mode
   - Description: "Developer testing options"

## 3. Implementation Details

### Architecture
```
features/settings/
├── data/
│   └── settings_repository.dart    # Data access for settings
├── domain/
│   ├── models/
│   │   └── app_settings.dart       # Settings model
│   └── usecases/
│       ├── get_settings.dart       # Get settings logic
│       └── update_settings.dart    # Update settings logic
└── presentation/
    ├── controllers/
    │   └── settings_controller.dart # Business logic
    ├── screens/
    │   └── settings_screen.dart     # Main screen UI
    └── widgets/
        ├── settings_category.dart   # Category widget
        ├── settings_toggle.dart     # Toggle widget
        ├── settings_input.dart      # Input field widget
        ├── settings_radio.dart      # Radio button widget
        └── settings_message.dart    # Message banner widget
```

### Key Components

1. **AppSettings Model**
   ```dart
   class AppSettings {
     // Language settings
     final bool languageEnabled;
     final String language; // english, spanish, french, etc.

     // Currency settings
     final bool currencyEnabled;
     final String currency; // GBP, USD, EUR, etc.

     // Alert settings
     final bool alertsEnabled;
     final bool alertThresholdEnabled;
     final double alertThreshold; // Default: 5.00
     final bool daysInAdvanceEnabled;
     final int daysInAdvance; // Default: 2

     // Notification settings
     final bool notificationsEnabled;
     final bool lowBalanceAlertsEnabled;
     final bool timeToTopUpAlertsEnabled;
     final bool timeToNoUnitsAlertsEnabled;
     final bool invalidRecordAlertsEnabled;

     // Reminder settings
     final bool remindersEnabled;
     final bool meterRemindersEnabled;
     final String reminderFrequency; // daily, weekly, bi_weekly, monthly
     final String reminderTime; // HH:MM format
     final DateTime? nextReminderDate;
     final DateTime? lastReminderTime;

     // Date settings
     final bool dateFormatEnabled;
     final String dateFormat; // DD-MM-YYYY, MM-DD-YYYY, YYYY-MM-DD
     final bool dateInfoEnabled;
     final String dateInfoType; // date_only, date_and_time

     // Appearance settings
     final bool appearanceEnabled;
     final String themeMode; // system_default, light_mode, dark_mode

     // Data Backup settings
     final bool dataBackupEnabled;

     // About settings
     final bool aboutEnabled;

     // Donate settings
     final bool donateEnabled;

     // Cost Analysis settings
     final bool costAnalysisEnabled;

     // Testing settings
     final bool testingEnabled;

     // Constructor and methods
   }

   enum ThemeMode {
     systemDefault,
     lightMode,
     darkMode
   }

   enum DateFormat {
     ddMmYyyy,
     mmDdYyyy,
     yyyyMmDd
   }

   enum DateInfoType {
     dateOnly,
     dateAndTime
   }
   ```

2. **SettingsController**
   ```dart
   class SettingsController extends ChangeNotifier {
     final SettingsRepository _repository;

     // State variables
     late AppSettings _settings;
     bool _isLoading = true;
     String? _error;

     // Constructor
     SettingsController(this._repository) {
       _loadSettings();
     }

     // Getters
     AppSettings get settings => _settings;
     bool get isLoading => _isLoading;
     String? get error => _error;

     // Methods
     Future<void> _loadSettings() async {
       try {
         _isLoading = true;
         notifyListeners();

         _settings = await _repository.getSettings();
         _error = null;
       } catch (e) {
         _error = e.toString();
       } finally {
         _isLoading = false;
         notifyListeners();
       }
     }

     Future<void> toggleAlerts(bool enabled) async {
       try {
         await _repository.updateAlertsSetting(enabled);
         _settings = _settings.copyWith(alertsEnabled: enabled);
         notifyListeners();
       } catch (e) {
         _error = e.toString();
         notifyListeners();
       }
     }

     Future<void> toggleDateFormat(bool enabled) async {
       try {
         await _repository.updateDateFormatSetting(enabled);
         _settings = _settings.copyWith(dateFormatEnabled: enabled);
         notifyListeners();
       } catch (e) {
         _error = e.toString();
         notifyListeners();
       }
     }

     Future<void> updateDateFormat(String format) async {
       try {
         await _repository.updateDateFormat(format);
         _settings = _settings.copyWith(dateFormat: format);
         notifyListeners();
       } catch (e) {
         _error = e.toString();
         notifyListeners();
       }
     }

     Future<void> toggleAppearance(bool enabled) async {
       try {
         await _repository.updateAppearanceSetting(enabled);
         _settings = _settings.copyWith(appearanceEnabled: enabled);
         notifyListeners();
       } catch (e) {
         _error = e.toString();
         notifyListeners();
       }
     }

     Future<void> updateThemeMode(String mode) async {
       try {
         await _repository.updateThemeMode(mode);
         _settings = _settings.copyWith(themeMode: mode);
         notifyListeners();
       } catch (e) {
         _error = e.toString();
         notifyListeners();
       }
     }

     // Similar methods for other settings
   }
   ```

3. **SettingsScreen**
   ```dart
   class SettingsScreen extends StatelessWidget {
     @override
     Widget build(BuildContext context) {
       return Consumer<SettingsController>(
         builder: (context, controller, _) {
           if (controller.isLoading) {
             return const LoadingIndicator();
           }

           if (controller.error != null) {
             return ErrorDisplay(message: controller.error!);
           }

           return Scaffold(
             appBar: AppBar(
               title: const Text('Settings'),
               backgroundColor: AppColors.darkGray,
               actions: [
                 IconButton(
                   icon: const Icon(Icons.arrow_back),
                   onPressed: () => Navigator.of(context).pop(),
                 ),
               ],
             ),
             body: Column(
               children: [
                 SettingsMessageBanner(),
                 Expanded(
                   child: ListView(
                     children: [
                       LanguageCategory(
                         settings: controller.settings,
                         onLanguageEnabledChanged: controller.toggleLanguage,
                         onLanguageChanged: controller.updateLanguage,
                       ),
                       CurrencyCategory(
                         settings: controller.settings,
                         onCurrencyEnabledChanged: controller.toggleCurrency,
                         onCurrencyChanged: controller.updateCurrency,
                       ),
                       AlertsAndNotificationsCategory(
                         settings: controller.settings,
                         onAlertsEnabledChanged: controller.toggleAlerts,
                         onAlertThresholdEnabledChanged: controller.toggleAlertThreshold,
                         onAlertThresholdChanged: controller.updateAlertThreshold,
                         onDaysInAdvanceEnabledChanged: controller.toggleDaysInAdvance,
                         onDaysInAdvanceChanged: controller.updateDaysInAdvance,
                         onNotificationsEnabledChanged: controller.toggleNotifications,
                         onLowBalanceAlertsEnabledChanged: controller.toggleLowBalanceAlerts,
                         onTimeToTopUpAlertsEnabledChanged: controller.toggleTimeToTopUpAlerts,
                         onTimeToNoUnitsAlertsEnabledChanged: controller.toggleTimeToNoUnitsAlerts,
                         onInvalidRecordAlertsEnabledChanged: controller.toggleInvalidRecordAlerts,
                         onRemindersEnabledChanged: controller.toggleReminders,
                         onMeterRemindersEnabledChanged: controller.toggleMeterReminders,
                         onReminderFrequencyChanged: controller.updateReminderFrequency,
                         onReminderTimeChanged: controller.updateReminderTime,
                       ),
                       DateSettingsCategory(
                         settings: controller.settings,
                         onDateFormatEnabledChanged: controller.toggleDateFormat,
                         onDateFormatChanged: controller.updateDateFormat,
                         onDateInfoEnabledChanged: controller.toggleDateInfo,
                         onDateInfoTypeChanged: controller.updateDateInfoType,
                       ),
                       AppearanceCategory(
                         settings: controller.settings,
                         onAppearanceEnabledChanged: controller.toggleAppearance,
                         onThemeModeChanged: controller.updateThemeMode,
                       ),
                       DataBackupCategory(
                         settings: controller.settings,
                         onDataBackupEnabledChanged: controller.toggleDataBackup,
                         onExportData: controller.exportData,
                         onImportData: controller.importData,
                         onClearData: controller.clearAllData,
                       ),
                       AboutCategory(
                         settings: controller.settings,
                         onAboutEnabledChanged: controller.toggleAbout,
                       ),
                       DonateCategory(
                         settings: controller.settings,
                         onDonateEnabledChanged: controller.toggleDonate,
                       ),
                       CostAnalysisCategory(
                         settings: controller.settings,
                         onCostAnalysisEnabledChanged: controller.toggleCostAnalysis,
                       ),
                       TestingCategory(
                         settings: controller.settings,
                         onTestingEnabledChanged: controller.toggleTesting,
                       ),
                     ],
                   ),
                 ),
               ],
             ),
           );
         },
       );
     }
   }
   ```

4. **SettingsCategory Widget**
   ```dart
   class SettingsCategory extends StatefulWidget {
     final String title;
     final IconData icon;
     final List<Widget> children;
     final bool initiallyExpanded;

     const SettingsCategory({
       Key? key,
       required this.title,
       required this.icon,
       required this.children,
       this.initiallyExpanded = false,
     }) : super(key: key);

     @override
     _SettingsCategoryState createState() => _SettingsCategoryState();
   }

   class _SettingsCategoryState extends State<SettingsCategory> {
     late bool _isExpanded;

     @override
     void initState() {
       super.initState();
       _isExpanded = widget.initiallyExpanded;
     }

     @override
     Widget build(BuildContext context) {
       return Card(
         margin: const EdgeInsets.all(8.0),
         shape: RoundedRectangleBorder(
           borderRadius: BorderRadius.circular(12.0),
         ),
         child: Column(
           children: [
             ListTile(
               leading: Icon(
                 widget.icon,
                 color: Colors.blue,
               ),
               title: Text(
                 widget.title,
                 style: const TextStyle(
                   fontWeight: FontWeight.bold,
                 ),
               ),
               trailing: Switch(
                 value: _isExpanded,
                 onChanged: (value) {
                   setState(() {
                     _isExpanded = value;
                   });
                 },
                 activeColor: Colors.blue,
                 inactiveThumbColor: Colors.grey,
                 inactiveTrackColor: Colors.grey.withOpacity(0.5),
               ),
             ),
             if (_isExpanded)
               Padding(
                 padding: const EdgeInsets.all(16.0),
                 child: Column(
                   crossAxisAlignment: CrossAxisAlignment.start,
                   children: widget.children,
                 ),
               ),
           ],
         ),
       );
     }
   }
   ```

## 4. Interaction Flow

1. **Opening Settings**
   - User taps Settings icon in the app bar
   - Settings screen loads with all categories collapsed
   - Message banner displays a helpful tip

2. **Expanding a Category**
   - User taps toggle switch next to a category
   - Category expands to show its settings
   - Other categories remain collapsed

3. **Changing Language**
   - User expands Language category
   - User enables Language toggle
   - Radio button list appears with current language selected
   - User selects desired language
   - Setting is saved immediately
   - UI updates to reflect the new language

4. **Changing Currency**
   - User expands Currency category
   - User enables Currency toggle
   - Radio button list appears with current currency selected
   - User selects desired currency
   - Setting is saved immediately
   - UI updates to reflect the new currency

5. **Setting Alert Threshold**
   - User expands Alerts & Notifications category
   - User enables Alert Threshold toggle
   - Input field appears with current threshold value
   - User enters new threshold value
   - Setting is saved immediately
   - Validation ensures value is between £1.00 and £999.00

6. **Setting Days in Advance**
   - User expands Alerts & Notifications category
   - User enables Days in Advance toggle
   - Input field appears with current days value
   - User enters new days value
   - Setting is saved immediately
   - Validation ensures value is between 1 and 99 days

7. **Configuring Notifications**
   - User expands Alerts & Notifications category
   - User enables Notifications toggle
   - Notification options appear
   - User toggles specific notification types on/off
   - Settings are saved immediately

8. **Setting Up Reminders**
   - User expands Alerts & Notifications category
   - User enables Reminders toggle
   - Reminder options appear
   - User selects reminder frequency and time
   - Settings are saved immediately
   - Next reminder date is calculated and displayed

9. **Changing Date Format**
   - User expands Date Settings category
   - User enables Date Format toggle
   - Radio button list appears with current format selected
   - User selects desired format (DD-MM-YYYY, MM-DD-YYYY, or YYYY-MM-DD)
   - Setting is saved immediately
   - UI updates to reflect the new date format

10. **Changing Theme**
    - User expands Appearance category
    - User enables Appearance toggle
    - Radio button list appears with current theme selected
    - User selects desired theme (System Default, Light Mode, or Dark Mode)
    - Setting is saved immediately
    - UI updates to reflect the new theme

11. **Using Data Backup**
    - User expands Data Backup category
    - User enables Data Backup toggle
    - Data management options appear
    - User taps Export Data
    - App exports data to a CSV file in the Downloads folder
    - Confirmation message appears

12. **Viewing Cost Analysis**
    - User expands Cost Analysis category
    - User enables Cost Analysis toggle
    - Cost analysis options appear
    - User selects desired time period (Day, Week, Month, Year, or Custom Range)
    - App displays cost analysis for the selected period

13. **Navigating Back to Homepage**
    - User taps back arrow in app bar
    - Settings are saved
    - User is returned to the Homepage

## 5. Validation Rules

1. **Language**
   - Must be one of the predefined languages (English, Spanish, French, etc.)

2. **Currency**
   - Must be one of the predefined currencies (GBP, USD, EUR, etc.)

3. **Alert Threshold**
   - Must be a numeric value between £1.00 and £999.00
   - Must be a valid currency amount with up to 2 decimal places

4. **Days in Advance**
   - Must be an integer between 1 and 99
   - Cannot be negative or zero

5. **Reminder Time**
   - Must be a valid time in 24-hour format (HH:MM)
   - Hours must be between 00 and 23
   - Minutes must be between 00 and 59

6. **Date Format**
   - Must be one of the predefined formats (DD-MM-YYYY, MM-DD-YYYY, YYYY-MM-DD)

7. **Theme Mode**
   - Must be one of the predefined modes (System Default, Light Mode, Dark Mode)

8. **CSV Import**
   - Must be a valid CSV file with the correct format
   - Must contain required columns (date, reading, top-up)
   - Values must be within valid ranges

9. **Cost Analysis Period**
   - Custom date range must have valid start and end dates
   - Start date must be before end date
   - Dates must be within the range of available data

## 6. Error Handling

1. **Input Validation Errors**
   - Display inline error messages below input fields
   - Highlight invalid fields in red
   - Prevent saving invalid values

2. **Loading Errors**
   - Display error message if settings cannot be loaded
   - Provide retry button

3. **Saving Errors**
   - Display toast/snackbar with error message
   - Keep UI in current state
   - Allow user to retry
