import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lekky/core/constants/preference_keys.dart';

void main() {
  group('Notification Deduplication Tests', () {
    late SharedPreferences prefs;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
    });

    tearDown(() async {
      await prefs.clear();
    });

    group('Last Notification Date Tracking', () {
      test('should store and retrieve last low balance notification date',
          () async {
        final testDate = DateTime(2024, 1, 15, 10, 30);

        // Store the date
        await prefs.setInt(
          PreferenceKeys.lastLowBalanceNotificationDate,
          testDate.millisecondsSinceEpoch,
        );

        // Retrieve and verify
        final storedTimestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        expect(storedTimestamp, equals(testDate.millisecondsSinceEpoch));

        final retrievedDate =
            DateTime.fromMillisecondsSinceEpoch(storedTimestamp!);
        expect(retrievedDate, equals(testDate));
      });

      test('should store and retrieve last time to top up notification date',
          () async {
        final testDate = DateTime(2024, 1, 16, 14, 45);

        // Store the date
        await prefs.setInt(
          PreferenceKeys.lastTimeToTopUpNotificationDate,
          testDate.millisecondsSinceEpoch,
        );

        // Retrieve and verify
        final storedTimestamp =
            prefs.getInt(PreferenceKeys.lastTimeToTopUpNotificationDate);
        expect(storedTimestamp, equals(testDate.millisecondsSinceEpoch));

        final retrievedDate =
            DateTime.fromMillisecondsSinceEpoch(storedTimestamp!);
        expect(retrievedDate, equals(testDate));
      });

      test('should return null when no notification date is stored', () async {
        final timestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        expect(timestamp, isNull);
      });
    });

    group('Deduplication Logic', () {
      test('should allow notification when no previous date exists', () async {
        // No previous notification date stored
        final timestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        expect(timestamp, isNull);

        // Should allow notification
        expect(timestamp == null, isTrue);
      });

      test('should prevent notification when sent within same calendar day',
          () async {
        final now = DateTime(2024, 1, 15, 14, 30);
        final sameDay = DateTime(2024, 1, 15, 10, 30);

        // Store notification from earlier same day
        await prefs.setInt(
          PreferenceKeys.lastLowBalanceNotificationDate,
          sameDay.millisecondsSinceEpoch,
        );

        // Check if should send notification using calendar day logic
        final lastTimestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp!);

        final isSameDay = lastDate.year == now.year &&
            lastDate.month == now.month &&
            lastDate.day == now.day;

        expect(isSameDay, isTrue);
        expect(!isSameDay, isFalse); // Should NOT send notification
      });

      test('should allow notification when sent on different calendar day',
          () async {
        final now = DateTime(2024, 1, 16, 8, 0);
        final previousDay = DateTime(2024, 1, 15, 23, 30);

        // Store notification from previous calendar day
        await prefs.setInt(
          PreferenceKeys.lastLowBalanceNotificationDate,
          previousDay.millisecondsSinceEpoch,
        );

        // Check if should send notification using calendar day logic
        final lastTimestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp!);

        final isSameDay = lastDate.year == now.year &&
            lastDate.month == now.month &&
            lastDate.day == now.day;

        expect(isSameDay, isFalse);
        expect(!isSameDay, isTrue); // Should send notification
      });

      test('should allow notification when sent multiple days ago', () async {
        final now = DateTime(2024, 1, 18, 12, 0);
        final threeDaysAgo = DateTime(2024, 1, 15, 12, 0);

        // Store notification from 3 days ago
        await prefs.setInt(
          PreferenceKeys.lastTimeToTopUpNotificationDate,
          threeDaysAgo.millisecondsSinceEpoch,
        );

        // Check if should send notification using calendar day logic
        final lastTimestamp =
            prefs.getInt(PreferenceKeys.lastTimeToTopUpNotificationDate);
        final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp!);

        final isSameDay = lastDate.year == now.year &&
            lastDate.month == now.month &&
            lastDate.day == now.day;

        expect(isSameDay, isFalse);
        expect(!isSameDay, isTrue); // Should send notification
      });
    });

    group('Edge Cases', () {
      test('should handle midnight boundary correctly', () async {
        // Test notification sent on previous day
        final previousDay = DateTime(2024, 1, 15, 23, 59);
        final nextDay = DateTime(2024, 1, 16, 0, 1);

        await prefs.setInt(
          PreferenceKeys.lastLowBalanceNotificationDate,
          previousDay.millisecondsSinceEpoch,
        );

        final lastTimestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp!);
        final daysSinceLastNotification = nextDay.difference(lastDate).inDays;

        // The difference is actually 0 days since it's less than 24 hours
        // But we want to allow notifications on different calendar days
        expect(daysSinceLastNotification, equals(0));

        // For calendar day comparison, we should check if it's a different day
        final isDifferentDay = lastDate.day != nextDay.day ||
            lastDate.month != nextDay.month ||
            lastDate.year != nextDay.year;
        expect(isDifferentDay, isTrue);
      });

      test('should handle same timestamp correctly', () async {
        final exactTime = DateTime(2024, 1, 15, 12, 0);

        await prefs.setInt(
          PreferenceKeys.lastLowBalanceNotificationDate,
          exactTime.millisecondsSinceEpoch,
        );

        final lastTimestamp =
            prefs.getInt(PreferenceKeys.lastLowBalanceNotificationDate);
        final lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp!);
        final daysSinceLastNotification = exactTime.difference(lastDate).inDays;

        expect(daysSinceLastNotification, equals(0));
        expect(daysSinceLastNotification >= 1, isFalse);
      });
    });

    group('Notification Type Mapping', () {
      test('should map notification types to correct preference keys', () {
        expect(PreferenceKeys.lastLowBalanceNotificationDate,
            equals('last_low_balance_notification_date'));
        expect(PreferenceKeys.lastTimeToTopUpNotificationDate,
            equals('last_time_to_top_up_notification_date'));
      });
    });
  });
}
