# NewLekky Theme Implementation Guide

This document provides a comprehensive guide to the color palette and theme implementation for the NewLekky app, covering both light and dark modes.

## Core Color Palette

### Primary Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Primary | #0288D1 | #42A5F5 | Main brand color, primary buttons, links |
| Secondary | #FFA000 | #90CAF9 | Secondary actions, highlights |
| Tertiary | #FFCA28 | #FFB74D | Badges, info chips, accents |
| Error | #D32F2F | #E57373 | Error messages, validation errors |
| Success | #388E3C | #66BB6A | Success messages, confirmations |
| Warning | #FFC107 | #FFD54F | Warning messages, alerts |
| Info | #2196F3 | #64B5F6 | Informational messages |

### Background Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Background | #F8F9FA | #121212 | Main app background |
| Surface | #FFFFFF | #1E1E1E | Cards, dialogs, elevated surfaces |
| Surface Variant | #E7E0EC | #242424 | Alternative surface color |
| Card Background | #FFFFFF (85% opacity) | #1E1E1E (85% opacity) | Card backgrounds with slight transparency |

### Text Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| On Primary | #FFFFFF | #000000 | Text on primary colored backgrounds |
| On Secondary | #000000 | #000000 | Text on secondary colored backgrounds |
| On Tertiary | #000000 | #000000 | Text on tertiary colored backgrounds |
| On Error | #FFFFFF | #000000 | Text on error colored backgrounds |
| On Background | #212121 | #E1E1E1 | Primary text on background |
| On Surface | #212121 | #E1E1E1 | Primary text on surface |
| Text Secondary | #757575 | #B0B0B0 | Secondary text, labels |

### Border and Outline Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Outline | #79747E | #938F99 | Borders, dividers |

### Screen-Specific Colors

| Screen | App Bar (Light) | App Bar (Dark) | Gradient (Light) | Gradient (Dark) |
|--------|----------------|----------------|------------------|-----------------|
| Home | #0288D1 | #42A5F5 | #003087 → #0057B8 | #0D47A1 → #2196F3 |
| History | #8E24AA | #9C27B0 | #8E24AA → #AB47BC | #7B1FA2 → #BA68C8 |
| Cost | #E65100 | #FF5722 | #E65100 → #F57C00 | #E64A19 → #FF8A65 |

### Table-Specific Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Table Reading Text | #003087 | #64B5F6 | Text for meter readings in tables |
| Table Top-Up Text | #FF9800 | #FFB74D | Text for top-ups in tables |
| Table Row Invalid | #FFF9C4 | #5D4037 | Background for invalid rows |
| Table Row Top-Up | #FFF8E1 | #455A64 | Background for top-up rows |
| Table Row Even | #FFFFFF | #2C2C2C | Background for even rows |
| Table Row Odd | #F5F5F5 | #212121 | Background for odd rows |
| Table Header | #E1E1E1 | #303030 | Background for table headers |

## Theme Implementation

The theme is implemented using Flutter's Material 3 design system with custom extensions for app-specific colors. The implementation follows these principles:

1. **Consistency**: Colors are consistently applied across the app for a cohesive look and feel.
2. **Accessibility**: Color contrasts meet WCAG 2.1 AA standards for readability.
3. **Adaptability**: The theme adapts seamlessly between light and dark modes.
4. **Simplicity**: The color palette is intentionally limited to maintain visual harmony.
5. **Functionality**: Colors are chosen to enhance usability and provide clear visual cues.

## Component-Specific Styling

### Buttons

- **Primary Button**: Blue background (#0288D1 in light, #42A5F5 in dark) with white text
- **Secondary Button**: White/dark background with blue text and outline
- **Danger Button**: Red background (#D32F2F in light, #E57373 in dark) with white text

### Cards

- **Standard Card**: White/dark background with subtle elevation
- **Highlighted Card**: Standard card with colored border based on context
- **Warning Card**: Light yellow/dark brown background for warnings

### Text Fields

- **Standard Input**: White/dark background with gray border
- **Focused Input**: White/dark background with primary color border
- **Error Input**: White/dark background with error color border

### Dialogs

- **Standard Dialog**: White/dark background with rounded corners
- **Alert Dialog**: Standard dialog with contextual icon and colors

## Usage Guidelines

1. Use primary color for main actions and brand identity
2. Use secondary color for complementary actions
3. Use tertiary color for accents and highlights
4. Use semantic colors (error, warning, success, info) consistently for their intended purposes
5. Maintain consistent text hierarchy using the defined text colors
6. Apply screen-specific colors only to their designated screens

## Implementation Notes

The theme is implemented through:
- `app_colors.dart`: Defines all color constants
- `app_theme.dart`: Configures the ThemeData for light and dark modes
- `theme_manager.dart`: Manages theme state and provides theme switching functionality

Custom theme extensions are used to provide app-specific colors that aren't part of the standard Material theme.
