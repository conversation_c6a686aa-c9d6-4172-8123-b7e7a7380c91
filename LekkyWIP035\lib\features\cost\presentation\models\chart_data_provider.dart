// File: lib/features/cost/presentation/models/chart_data_provider.dart
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/average_calculator.dart';
import '../../../../core/utils/cost_calculator.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../domain/models/cost_period.dart';
import 'chart_data.dart';

/// Provider for chart data
class ChartDataProvider {
  /// Generates chart data for the given period
  static List<ChartData> generateChartData({
    required List<MeterEntry> entries,
    required CostPeriod period,
    required DateTime? fromDate,
    required DateTime? toDate,
  }) {
    if (entries.isEmpty) {
      return [];
    }

    // Sort entries by timestamp
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Determine the date range
    final effectiveFromDate =
        fromDate ?? DateTime.now().subtract(Duration(days: period.days));
    final effectiveToDate = toDate ?? DateTime.now();

    // For custom periods, generate data points based on the date range
    if (period == CostPeriod.custom) {
      return _generateCustomPeriodData(
        sortedEntries,
        effectiveFromDate,
        effectiveToDate,
      );
    }

    // For standard periods, generate data points based on the period type
    switch (period.name) {
      case 'Day':
        return _generateHourlyData(
            sortedEntries, effectiveFromDate, effectiveToDate);
      case 'Week':
        return _generateDailyData(
            sortedEntries, effectiveFromDate, effectiveToDate);
      case 'Month':
        return _generateWeeklyData(
            sortedEntries, effectiveFromDate, effectiveToDate);
      case 'Year':
        return _generateMonthlyData(
            sortedEntries, effectiveFromDate, effectiveToDate);
      default:
        return _generateDailyData(
            sortedEntries, effectiveFromDate, effectiveToDate);
    }
  }

  /// Generates data points for a custom period
  static List<ChartData> _generateCustomPeriodData(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    // Calculate the total number of days in the range
    final totalDays = DateTimeUtils.daysBetween(fromDate, toDate);

    // Determine the appropriate interval based on the number of days
    if (totalDays <= 1) {
      // For a single day, show hourly data
      return _generateHourlyData(entries, fromDate, toDate);
    } else if (totalDays <= 7) {
      // For up to a week, show daily data
      return _generateDailyData(entries, fromDate, toDate);
    } else if (totalDays <= 31) {
      // For up to a month, show weekly data
      return _generateWeeklyData(entries, fromDate, toDate);
    } else {
      // For longer periods, show monthly data
      return _generateMonthlyData(entries, fromDate, toDate);
    }
  }

  /// Generates hourly data points
  static List<ChartData> _generateHourlyData(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final chartData = <ChartData>[];

    // Create a data point for each hour in the range
    final startHour =
        DateTime(fromDate.year, fromDate.month, fromDate.day, fromDate.hour);
    final endHour =
        DateTime(toDate.year, toDate.month, toDate.day, toDate.hour);

    for (var hour = startHour;
        hour.isBefore(endHour) || hour.isAtSameMomentAs(endHour);
        hour = hour.add(const Duration(hours: 1))) {
      final nextHour = hour.add(const Duration(hours: 1));

      // Calculate cost for this hour
      final cost = CostCalculator.calculateCost(entries, hour, nextHour);

      // Calculate usage for this hour
      final usage = _calculateUsageForPeriod(entries, hour, nextHour);

      chartData.add(ChartData(
        date: hour,
        usage: usage,
        cost: cost,
      ));
    }

    return chartData;
  }

  /// Generates daily data points
  static List<ChartData> _generateDailyData(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final chartData = <ChartData>[];

    // Create a data point for each day in the range
    final startDay = DateTime(fromDate.year, fromDate.month, fromDate.day);
    final endDay = DateTime(toDate.year, toDate.month, toDate.day);

    for (var day = startDay;
        day.isBefore(endDay) || day.isAtSameMomentAs(endDay);
        day = day.add(const Duration(days: 1))) {
      final nextDay = day.add(const Duration(days: 1));

      // Calculate cost for this day
      final cost = CostCalculator.calculateCost(entries, day, nextDay);

      // Calculate usage for this day
      final usage = _calculateUsageForPeriod(entries, day, nextDay);

      chartData.add(ChartData(
        date: day,
        usage: usage,
        cost: cost,
      ));
    }

    return chartData;
  }

  /// Generates weekly data points
  static List<ChartData> _generateWeeklyData(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final chartData = <ChartData>[];

    // Create a data point for each week in the range
    final startWeek = DateTimeUtils.startOfWeek(fromDate);
    final endWeek = DateTimeUtils.endOfWeek(toDate);

    for (var week = startWeek;
        week.isBefore(endWeek);
        week = week.add(const Duration(days: 7))) {
      final nextWeek = week.add(const Duration(days: 7));

      // Calculate cost for this week
      final cost = CostCalculator.calculateCost(entries, week, nextWeek);

      // Calculate usage for this week
      final usage = _calculateUsageForPeriod(entries, week, nextWeek);

      chartData.add(ChartData(
        date: week,
        usage: usage,
        cost: cost,
      ));
    }

    return chartData;
  }

  /// Generates monthly data points
  static List<ChartData> _generateMonthlyData(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final chartData = <ChartData>[];

    // Create a data point for each month in the range
    var currentMonth = DateTime(fromDate.year, fromDate.month, 1);
    final endMonth = DateTime(toDate.year, toDate.month, 1);

    while (currentMonth.isBefore(endMonth) ||
        currentMonth.isAtSameMomentAs(endMonth)) {
      final nextMonth = DateTime(currentMonth.year, currentMonth.month + 1, 1);

      // Calculate cost for this month
      final cost =
          CostCalculator.calculateCost(entries, currentMonth, nextMonth);

      // Calculate usage for this month
      final usage = _calculateUsageForPeriod(entries, currentMonth, nextMonth);

      chartData.add(ChartData(
        date: currentMonth,
        usage: usage,
        cost: cost,
      ));

      currentMonth = nextMonth;
    }

    return chartData;
  }

  /// Calculates the usage for a specific period
  static double _calculateUsageForPeriod(
    List<MeterEntry> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    // Filter entries to only include those within the period
    final periodEntries = entries
        .where((entry) =>
            (entry.timestamp.isAfter(fromDate) ||
                entry.timestamp.isAtSameMomentAs(fromDate)) &&
            entry.timestamp.isBefore(toDate))
        .toList();

    if (periodEntries.isEmpty) {
      // If no entries in this period, estimate based on average
      final totalAverage = AverageCalculator.calculateTotalAverage(entries);
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return totalAverage * days;
    }

    // Calculate usage based on meter readings and top-ups
    final meterReadings =
        periodEntries.where((e) => e.amountToppedUp == 0).toList();

    if (meterReadings.length < 2) {
      // If not enough meter readings, estimate based on average
      final totalAverage = AverageCalculator.calculateTotalAverage(entries);
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return totalAverage * days;
    }

    // Sort readings by timestamp
    meterReadings.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Calculate usage: first reading + top-ups - last reading
    final firstReading = meterReadings.first;
    final lastReading = meterReadings.last;

    // Sum top-ups between first and last readings
    double sumTopUps = 0.0;
    for (final entry in periodEntries) {
      if (entry.amountToppedUp > 0 &&
          entry.timestamp.isAfter(firstReading.timestamp) &&
          entry.timestamp.isBefore(lastReading.timestamp)) {
        sumTopUps += entry.amountToppedUp;
      }
    }

    final usage = firstReading.reading - lastReading.reading + sumTopUps;
    return usage > 0 ? usage : 0.0;
  }
}
