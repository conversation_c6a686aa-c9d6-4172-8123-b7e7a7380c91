// File: lib/core/utils/permission_helper.dart
import 'package:permission_handler/permission_handler.dart';
import 'logger.dart';

/// Helper class for handling permissions
class PermissionHelper {
  final Logger _logger = Logger();
  
  /// Constructor
  PermissionHelper();
  
  /// Check and request storage permission
  /// Returns true if permission is granted, false otherwise
  Future<bool> checkAndRequestStoragePermission() async {
    try {
      _logger.i('Checking storage permission...');
      
      // Check current status
      PermissionStatus status = await Permission.storage.status;
      
      if (status.isGranted) {
        _logger.i('Storage permission already granted');
        return true;
      }
      
      // Request permission
      _logger.i('Requesting storage permission...');
      status = await Permission.storage.request();
      
      if (status.isGranted) {
        _logger.i('Storage permission granted');
        return true;
      } else if (status.isPermanentlyDenied) {
        _logger.w('Storage permission permanently denied');
        return false;
      } else {
        _logger.w('Storage permission denied: $status');
        return false;
      }
    } catch (e) {
      _logger.e('Error checking/requesting storage permission', details: e.toString());
      return false;
    }
  }
  
  /// Check if storage permission is granted
  /// Returns true if permission is granted, false otherwise
  Future<bool> hasStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking storage permission', details: e.toString());
      return false;
    }
  }
  
  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      _logger.e('Error opening app settings', details: e.toString());
      return false;
    }
  }
}
