// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$themeHash() => r'7e34ae5b5c326667ae2ebba2a231b24a744ff8ec';

/// Comprehensive theme provider using Riverpod with AppColors integration
///
/// Copied from [Theme].
@ProviderFor(Theme)
final themeProvider =
    AutoDisposeAsyncNotifierProvider<Theme, ThemeState>.internal(
  Theme.new,
  name: r'themeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$themeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Theme = AutoDisposeAsyncNotifier<ThemeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
