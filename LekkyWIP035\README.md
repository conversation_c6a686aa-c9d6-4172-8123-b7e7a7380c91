# Lekky - Prepaid Electricity Meter Tracker

Lekky is a personal prepaid electricity meter tracking application designed for offline use. It helps users track meter readings, monitor usage patterns, and manage electricity consumption efficiently.

## Features

- Track meter readings and top-ups
- Calculate usage averages (recent and total)
- Project remaining days based on usage patterns
- Provide notifications for readings and low balance
- Support data validation and repair
- Offer comprehensive settings customization
- Visualize usage and cost data

## Implementation Phases

The implementation is divided into four phases:

1. **Foundation**
   - Project setup and architecture
   - Database implementation
   - State management
   - Navigation and core UI

2. **Core Features**
   - Onboarding and setup
   - Home screen and entry system
   - History and cost modules
   - Basic settings

3. **Advanced Features**
   - Data visualization
   - Notification system
   - Data management
   - Advanced calculations

4. **Polish & Testing**
   - Comprehensive testing
   - Performance optimization
   - Accessibility enhancements
   - Final UI refinements

## Architecture

Lekky follows a feature-first modular architecture with clear separation of concerns:

```
lib/
├── core/                  # Core functionality used across the app
│   ├── constants/         # App-wide constants
│   ├── error/             # Error handling
│   ├── navigation/        # Navigation service and routes
│   ├── theme/             # Theme configuration
│   ├── utils/             # Utility functions
│   └── widgets/           # Shared widgets
│
├── features/              # Feature modules
│   ├── home/              # Home feature
│   │   ├── presentation/  # UI components
│   │   ├── domain/        # Feature-specific business logic
│   │   └── data/          # Feature-specific data handling
│   ├── history/           # History feature
│   ├── cost/              # Cost feature
│   ├── settings/          # Settings feature
│   └── ...                # Other features
│
└── main.dart              # App entry point
```

## Technology Stack

- **Framework**: Flutter (latest stable)
- **State Management**: Riverpod for state management
- **Database**: SQLite with sqflite for data persistence
- **Preferences**: SharedPreferences for settings storage
- **Navigation**: go_router
- **Dependency Injection**: get_it with injectable

## Getting Started

To set up the development environment:

1. Install Flutter (latest stable version)
2. Clone this repository
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the app in debug mode

## Development Guidelines

- Follow the feature-first modular architecture
- Use Riverpod for state management
- Implement comprehensive validation for all data
- Write unit tests for business logic
- Create widget tests for UI components
- Ensure accessibility compliance
- Optimize for performance with large datasets

## Theme System

Lekky uses a comprehensive theming system that supports both light and dark modes. The theme is designed to be consistent across the app while providing visual differentiation between different screens (Home, History, Cost).

For detailed information about the theme system, see the [Theme Implementation Guide](docs/theme_implementation_guide.md).

## Data Model

Lekky uses a SQLite database with the following main tables:

- **meter_readings**: Stores meter readings with timestamps
- **top_ups**: Records top-up amounts and dates
- **averages**: Stores precomputed usage averages
- **settings**: Stores app configuration

## Limitations

- Designed for offline use only
- Handles up to 630 meter readings
- For personal use on a single device
- No cloud backup or synchronization

## License

This project is for personal use only and is not licensed for distribution.

## Acknowledgments

- Flutter team for the amazing framework
- Contributors to the open-source packages used in this project
