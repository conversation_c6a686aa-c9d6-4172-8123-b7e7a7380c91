// File: lib/features/settings/presentation/widgets/expandable_settings_category.dart
import 'package:flutter/material.dart';
import '../../domain/models/settings_category.dart';
import 'settings_sub_branch_item.dart';

/// A widget that displays an expandable settings category
class ExpandableSettingsCategory extends StatefulWidget {
  /// The settings category to display
  final SettingsCategory category;

  /// Whether the category is expanded
  final bool isExpanded;

  /// Callback when the expansion state changes
  final Function(bool) onExpansionChanged;

  /// Constructor
  const ExpandableSettingsCategory({
    Key? key,
    required this.category,
    required this.isExpanded,
    required this.onExpansionChanged,
  }) : super(key: key);

  @override
  State<ExpandableSettingsCategory> createState() =>
      _ExpandableSettingsCategoryState();
}

class _ExpandableSettingsCategoryState extends State<ExpandableSettingsCategory>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (widget.isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ExpandableSettingsCategory oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final cardColor = isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.black54;

    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      color: cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          // Header with toggle switch
          InkWell(
            onTap: () => widget.onExpansionChanged(!widget.isExpanded),
            borderRadius: BorderRadius.circular(12.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        widget.category.icon,
                        color: widget.category.iconColor,
                        size: 24,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.category.title,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: textColor,
                              ),
                            ),
                            if (widget.category.currentValue != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                widget.category.currentValue!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: subtitleColor,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Switch(
                        value: widget.category.isEnabled,
                        onChanged: widget.category.onToggle,
                        activeColor: Colors.blue,
                        activeTrackColor: Colors.blue.withOpacity(0.5),
                        inactiveThumbColor: Colors.grey,
                        inactiveTrackColor: Colors.grey.withOpacity(0.5),
                      ),
                    ],
                  ),
                  if (widget.category.subtitle != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      widget.category.subtitle!,
                      style: TextStyle(
                        fontSize: 14,
                        color: subtitleColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Expandable content with sub-branches
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: widget.category.isEnabled && widget.isExpanded
                ? Padding(
                    padding: const EdgeInsets.only(
                      left: 16.0,
                      right: 16.0,
                      bottom: 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: widget.category.subBranches.map((subBranch) {
                        return SettingsSubBranchItem(
                          subBranch: subBranch,
                          onTap: () {
                            Navigator.of(context)
                                .pushNamed(subBranch.routePath);
                          },
                        );
                      }).toList(),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
