import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/error_state.dart';
import '../models/app_error.dart';
import '../utils/logger.dart';

part 'error_provider.g.dart';

/// Global error handling provider
@riverpod
class GlobalError extends _$GlobalError {
  @override
  ErrorState build() {
    return ErrorState.initial();
  }

  /// Handle a new error
  void handleError(Object error, StackTrace? stackTrace, {
    bool showDialog = false,
    bool showBanner = true,
    String? context,
  }) {
    final appError = AppError.fromException(error);
    
    // Log the error
    Logger.error(
      'Global error${context != null ? ' in $context' : ''}: ${appError.message}',
      stackTrace,
    );

    // Update state
    state = state.copyWith(
      currentError: appError,
      showErrorDialog: showDialog,
      showErrorBanner: showBanner,
      recentErrors: [
        appError,
        ...state.recentErrors.take(9), // Keep last 10 errors
      ],
    );
  }

  /// Clear the current error
  void clearError() {
    state = state.copyWith(
      currentError: null,
      showErrorDialog: false,
      showErrorBanner: false,
    );
  }

  /// Dismiss error dialog
  void dismissDialog() {
    state = state.copyWith(showErrorDialog: false);
  }

  /// Dismiss error banner
  void dismissBanner() {
    state = state.copyWith(showErrorBanner: false);
  }

  /// Handle validation error
  void handleValidationError(String message, {String? field}) {
    final error = AppError.validation(message, field: field);
    handleError(error, null, showBanner: true);
  }

  /// Handle network error
  void handleNetworkError(String message) {
    final error = AppError.network(message);
    handleError(error, null, showDialog: true);
  }

  /// Handle database error
  void handleDatabaseError(String message) {
    final error = AppError.database(message);
    handleError(error, null, showBanner: true);
  }

  /// Toggle error reporting
  void toggleErrorReporting(bool enabled) {
    state = state.copyWith(errorReportingEnabled: enabled);
  }

  /// Get recent errors for debugging
  List<AppError> getRecentErrors() {
    return state.recentErrors;
  }

  /// Clear all errors
  void clearAllErrors() {
    state = ErrorState.initial().copyWith(
      errorReportingEnabled: state.errorReportingEnabled,
    );
  }
}
