import 'package:flutter/material.dart';
import '../controllers/entry_controller.dart';

/// A segmented control for selecting the entry type
class EntryTypeSelector extends StatelessWidget {
  /// The currently selected entry type
  final EntryType selectedType;

  /// Callback when the entry type changes
  final ValueChanged<EntryType> onTypeChanged;

  /// Whether the selector is enabled
  final bool enabled;

  /// Constructor
  const EntryTypeSelector({
    Key? key,
    required this.selectedType,
    required this.onTypeChanged,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildSegment(
            context,
            EntryType.meterReading,
            'Meter Reading',
            Icons.speed,
          ),
          Container(
            width: 1,
            height: 40,
            color: theme.colorScheme.outline.withOpacity(0.5),
          ),
          _buildSegment(
            context,
            EntryType.topUp,
            'Top-up',
            Icons.add_card,
          ),
        ],
      ),
    );
  }

  /// Build a single segment of the control
  Widget _buildSegment(
    BuildContext context,
    EntryType type,
    String label,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final isSelected = selectedType == type;

    // Use different colors based on entry type when selected
    final selectedBackgroundColor = type == EntryType.topUp
        ? theme.colorScheme.secondary // Orange for top-up
        : theme.colorScheme.primary; // Blue for meter reading

    final selectedTextColor = type == EntryType.topUp
        ? theme.colorScheme.onSecondary // Text color on orange
        : theme.colorScheme.onPrimary; // Text color on blue

    return Expanded(
      child: InkWell(
        onTap: enabled ? () => onTypeChanged(type) : null,
        borderRadius: BorderRadius.horizontal(
          left: type == EntryType.meterReading
              ? const Radius.circular(8)
              : Radius.zero,
          right:
              type == EntryType.topUp ? const Radius.circular(8) : Radius.zero,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? selectedBackgroundColor : Colors.transparent,
            borderRadius: BorderRadius.horizontal(
              left: type == EntryType.meterReading
                  ? const Radius.circular(7)
                  : Radius.zero,
              right: type == EntryType.topUp
                  ? const Radius.circular(7)
                  : Radius.zero,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: isSelected
                    ? selectedTextColor
                    : theme.colorScheme.onSurface,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: isSelected
                      ? selectedTextColor
                      : theme.colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
