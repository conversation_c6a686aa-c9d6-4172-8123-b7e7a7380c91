// File: lib/features/cost/domain/models/date_range.dart
import 'cost_period.dart';

/// Represents a date range for cost calculations
class DateRange {
  /// Start date of the range
  final DateTime startDate;

  /// End date of the range
  final DateTime endDate;

  /// Constructor
  const DateRange({
    required this.startDate,
    required this.endDate,
  });

  /// Default constructor with current date
  DateRange.now()
      : startDate = DateTime.now(),
        endDate = DateTime.now();

  /// Create a date range for a specific period
  factory DateRange.forPeriod(CostPeriod period) {
    final now = DateTime.now();

    switch (period) {
      case CostPeriod.pastDay:
        return DateRange(
          startDate: now.subtract(const Duration(days: 1)),
          endDate: now,
        );
      case CostPeriod.pastWeek:
        return DateRange(
          startDate: now.subtract(const Duration(days: 7)),
          endDate: now,
        );
      case CostPeriod.pastMonth:
        return DateRange(
          startDate: now.subtract(const Duration(days: 30)),
          endDate: now,
        );
      case CostPeriod.pastYear:
        return DateRange(
          startDate: now.subtract(const Duration(days: 365)),
          endDate: now,
        );
      case CostPeriod.futureDay:
        return DateRange(
          startDate: now,
          endDate: now.add(const Duration(days: 1)),
        );
      case CostPeriod.futureWeek:
        return DateRange(
          startDate: now,
          endDate: now.add(const Duration(days: 7)),
        );
      case CostPeriod.futureMonth:
        return DateRange(
          startDate: now,
          endDate: now.add(const Duration(days: 30)),
        );
      case CostPeriod.futureYear:
        return DateRange(
          startDate: now,
          endDate: now.add(const Duration(days: 365)),
        );
      case CostPeriod.custom:
        return DateRange(startDate: now, endDate: now);
    }
  }

  /// Create a custom date range
  DateRange.custom({
    required this.startDate,
    required this.endDate,
  });

  /// Get the number of days in the range
  int get days => endDate.difference(startDate).inDays + 1;

  /// Check if the range is valid
  bool get isValid =>
      startDate.isBefore(endDate) || startDate.isAtSameMomentAs(endDate);

  /// Copy with new dates
  DateRange copyWith({
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return DateRange(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}
