// File: lib/features/settings/domain/models/settings_sub_branch.dart
import 'package:flutter/material.dart';

/// Model representing a sub-branch in a settings category
class SettingsSubBranch {
  /// The title of the sub-branch
  final String title;
  
  /// The icon to display for the sub-branch
  final IconData icon;
  
  /// The color of the icon
  final Color iconColor;
  
  /// The route to navigate to when the sub-branch is tapped
  final String routePath;
  
  /// Optional subtitle to display
  final String? subtitle;
  
  /// Optional current value to display
  final String? currentValue;
  
  /// Constructor
  const SettingsSubBranch({
    required this.title,
    required this.icon,
    required this.iconColor,
    required this.routePath,
    this.subtitle,
    this.currentValue,
  });
}
