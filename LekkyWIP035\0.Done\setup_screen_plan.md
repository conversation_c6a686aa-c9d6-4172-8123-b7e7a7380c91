# Setup Screen Implementation Plan

## Overview
The Setup screen allows users to configure their preferences for the app, including date formats, alert settings, meter readings, and regional preferences. This screen appears after the welcome screen for first-time users and can also be accessed from the settings menu later.

## Visual Design

### General Layout
- **Background**: Light gray (`#F5F5F5`)
- **Header**: Dark gray (`#424242`) with "Setup" title in white
- **Sections**: Separated by cards with rounded corners and subtle shadows
- **Section Headers**: Blue accent color with icons
- **Dividers**: Light gray lines between subsections

### Typography
- **Section Headers**: 18sp, medium weight, accent blue
- **Section Descriptions**: 14sp, regular weight, dark gray
- **Option Labels**: 16sp, regular weight, black
- **Examples/Hints**: 14sp, light weight, medium gray
- **Info Notices**: 14sp, regular weight, in yellow notice boxes

## Components

### 1. Date Settings Section

#### Date Format Subsection
- **Header**: "Date Format" (18sp, bold)
- **Description**: "Choose how dates will be displayed throughout the app."
- **Options**: Radio button group with 3 options:
  - DD-MM-YYYY (Example: 11-05-2025)
  - MM-DD-YYYY (Example: 05-11-2025)
  - YYYY-MM-DD (Example: 2025-05-11)
- **Default**: DD-MM-YYYY

#### Date Information Subsection
- **Header**: "Date Information" (18sp, bold)
- **Description**: "Choose whether to show just the date or both date and time for your meter readings."
- **Options**: Radio button group with 2 options:
  - Date only (Example: 11-05-2025)
  - Date and time (Example: 11-05-2025 17:28)
- **Default**: Date only

### 2. Alert Settings Section

#### Alert Threshold Subsection
- **Header**: "Alert Threshold" (18sp, bold)
- **Description**: "You will be notified when your balance falls below this amount."
- **Input**: Text field with currency symbol prefix
  - Validation: Must be between £1.00 and £999.00
  - Default: £5.00
- **Info Notice**: Yellow box with text "Low balance alerts will be active after you enter your first meter reading."
- **Hint**: "Tip: Set this to the amount you typically top up with to get reminders at the right time."

#### Days in Advance Subsection
- **Header**: "Days in Advance" (18sp, bold)
- **Description**: "How many days in advance should we notify you about low balance?"
- **Input**: Text field for numeric input
  - Validation: Must be between 1 and 99
  - Default: 2
- **Info Notice**: Yellow box with text "Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage."
- **Hint**: "Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days."

### 3. Meter Reading Section

#### First Meter Reading Subsection
- **Header**: "First Meter Reading" (18sp, bold)
- **Description**: "Enter your initial meter credit (leave empty if not applicable)"
- **Input**: Text field for numeric input
  - Optional field
  - Placeholder: "First Meter Reading"
- **Label**: "Optional" below the input field
- **Hint**: "Leave blank if you don't want to track your initial credit"

### 4. Region Settings Section

#### Language Subsection
- **Header**: "Language" (18sp, bold)
- **Description**: "Select your preferred language for the app interface."
- **Options**: Radio button group with options:
  - English
  - Spanish
  - French
  - German
  - Italian
  - Portuguese
  - Russian
  - Chinese
  - Japanese
- **Default**: English

#### Currency Subsection
- **Header**: "Currency" (18sp, bold)
- **Description**: "Select the currency for your meter readings"
- **Options**: Radio button group with options in a grid layout:
  - $ (USD)
  - € (EUR)
  - CN¥ (CNY)
  - ₹ (INR)
  - ¥ (JPY)
  - £ (GBP)
  - R$ (BRL)
  - ₽ (RUB)
  - Rp (IDR)
  - C$ (CAD)
  - A$ (AUD)
  - Mex$ (MXN)
- **Default**: £ (GBP)
- **Hint**: "Select your currency symbol from the list"

## Navigation

- **Previous/Back**: Returns to the Welcome screen (for first-time setup) or Settings screen (when accessed later)
- **Next/Continue**: Button at bottom of screen to proceed to the next setup step or finish setup
- **Skip**: Option to skip optional sections

## Data Model

### SetupPreferences Class
```dart
class SetupPreferences {
  // Date Settings
  DateFormat dateFormat;
  bool showTimeWithDate;
  
  // Alert Settings
  double alertThreshold;
  int daysInAdvance;
  
  // Meter Reading
  double? initialMeterReading;
  
  // Region Settings
  String language;
  String currency;
  String currencySymbol;
  
  // Constructor with defaults
  SetupPreferences({
    this.dateFormat = DateFormat.ddMMyyyy,
    this.showTimeWithDate = false,
    this.alertThreshold = 5.0,
    this.daysInAdvance = 2,
    this.initialMeterReading,
    this.language = 'English',
    this.currency = 'GBP',
    this.currencySymbol = '£',
  });
  
  // Methods to save/load from SharedPreferences
  Future<void> saveToPreferences() async {...}
  static Future<SetupPreferences> loadFromPreferences() async {...}
}
```

### DateFormat Enum
```dart
enum DateFormat {
  ddMMyyyy, // DD-MM-YYYY
  mmDDyyyy, // MM-DD-YYYY
  yyyyMMdd, // YYYY-MM-DD
}
```

## Implementation Details

### Architecture
```
features/setup/
├── domain/
│   ├── models/
│   │   ├── setup_preferences.dart
│   │   └── date_format.dart
│   └── usecases/
│       ├── save_preferences.dart
│       └── load_preferences.dart
├── presentation/
│   ├── controllers/
│   │   └── setup_controller.dart
│   ├── screens/
│   │   └── setup_screen.dart
│   └── widgets/
│       ├── date_settings_card.dart
│       ├── alert_settings_card.dart
│       ├── meter_reading_card.dart
│       ├── region_settings_card.dart
│       ├── setup_section_header.dart
│       ├── info_notice.dart
│       ├── radio_option.dart
│       └── currency_option.dart
└── data/
    └── repositories/
        └── preferences_repository.dart
```

### Key Components

#### SetupController
```dart
class SetupController extends ChangeNotifier {
  SetupPreferences preferences = SetupPreferences();
  bool isLoading = false;
  String? error;
  
  // Date Format methods
  void setDateFormat(DateFormat format) {
    preferences.dateFormat = format;
    notifyListeners();
  }
  
  void setShowTimeWithDate(bool show) {
    preferences.showTimeWithDate = show;
    notifyListeners();
  }
  
  // Alert Settings methods
  void setAlertThreshold(double value) {
    if (value >= 1.0 && value <= 999.0) {
      preferences.alertThreshold = value;
      notifyListeners();
    }
  }
  
  void setDaysInAdvance(int days) {
    if (days >= 1 && days <= 99) {
      preferences.daysInAdvance = days;
      notifyListeners();
    }
  }
  
  // Meter Reading methods
  void setInitialMeterReading(double? value) {
    preferences.initialMeterReading = value;
    notifyListeners();
  }
  
  // Region Settings methods
  void setLanguage(String language) {
    preferences.language = language;
    notifyListeners();
  }
  
  void setCurrency(String currency, String symbol) {
    preferences.currency = currency;
    preferences.currencySymbol = symbol;
    notifyListeners();
  }
  
  // Save all preferences
  Future<bool> savePreferences() async {
    try {
      isLoading = true;
      notifyListeners();
      
      await preferences.saveToPreferences();
      
      isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      isLoading = false;
      error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  // Load saved preferences
  Future<void> loadPreferences() async {
    try {
      isLoading = true;
      notifyListeners();
      
      preferences = await SetupPreferences.loadFromPreferences();
      
      isLoading = false;
      notifyListeners();
    } catch (e) {
      isLoading = false;
      error = e.toString();
      notifyListeners();
    }
  }
}
```

## Validation Rules

1. **Alert Threshold**
   - Must be a valid number
   - Must be between £1.00 and £999.00
   - Should handle different currency symbols

2. **Days in Advance**
   - Must be a valid integer
   - Must be between 1 and 99

3. **First Meter Reading**
   - Optional field
   - If provided, must be a valid number
   - Should not be negative

## User Flow

1. User arrives at Setup screen from Welcome screen (first-time) or Settings (later)
2. User configures date format and information preferences
3. User sets alert threshold and days in advance values
4. User optionally enters initial meter reading
5. User selects language and currency preferences
6. User taps Continue/Save button
7. App validates all inputs
8. If validation passes, preferences are saved and user proceeds to next screen
9. If validation fails, error messages are shown for invalid fields

## Testing Plan

### Unit Tests
- Test validation logic for all input fields
- Test saving and loading preferences
- Test default values

### Widget Tests
- Test rendering of all setup sections
- Test radio button selection
- Test text field input and validation
- Test navigation between sections

### Integration Tests
- Test complete setup flow
- Test saving preferences and retrieving them later

## Accessibility Considerations
- Ensure all text meets contrast requirements
- Add semantic labels for screen readers
- Support dynamic text sizing
- Ensure touch targets are at least 48x48dp
