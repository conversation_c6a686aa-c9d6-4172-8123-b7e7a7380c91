import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../home/<USER>/providers/dashboard_provider.dart';
import '../../../home/<USER>/models/dashboard_state.dart';
import '../../domain/models/notification.dart';

part 'notification_evaluator_provider.g.dart';

/// Provider that evaluates notification conditions using dashboard calculations
@riverpod
class NotificationEvaluator extends _$NotificationEvaluator {
  @override
  Future<List<AppNotification>> build() async {
    // Watch both dashboard and settings for reactive updates
    final dashboardAsync = ref.watch(dashboardProvider);
    final settingsAsync = ref.watch(settingsProvider);

    return await dashboardAsync.when(
      data: (dashboardState) async {
        return await settingsAsync.when(
          data: (settings) async {
            return _evaluateConditions(dashboardState, settings);
          },
          loading: () => <AppNotification>[],
          error: (_, __) => <AppNotification>[],
        );
      },
      loading: () => <AppNotification>[],
      error: (_, __) => <AppNotification>[],
    );
  }

  /// Evaluate all notification conditions using dashboard calculations
  List<AppNotification> _evaluateConditions(
    DashboardState dashboardState,
    dynamic settings,
  ) {
    final notifications = <AppNotification>[];

    try {
      // Check low balance condition (< 24 hours to meter zero)
      if (settings.lowBalanceAlertsEnabled) {
        final lowBalanceNotification = _checkLowBalanceCondition(
          dashboardState,
        );
        if (lowBalanceNotification != null) {
          notifications.add(lowBalanceNotification);
        }
      }

      // Check time to top up condition (< 24 hours to alert threshold)
      if (settings.timeToTopUpAlertsEnabled) {
        final topUpNotification = _checkTimeToTopUpCondition(
          dashboardState,
          settings.alertThreshold,
          settings.daysInAdvance,
        );
        if (topUpNotification != null) {
          notifications.add(topUpNotification);
        }
      }

      // Invalid record alerts would be handled separately by validation system
      // as they don't depend on dashboard calculations

      Logger.info(
          'NotificationEvaluator: Evaluated ${notifications.length} notifications');
    } catch (e) {
      Logger.error('NotificationEvaluator: Error evaluating conditions: $e');
    }

    return notifications;
  }

  /// Check low balance condition using dashboard calculation
  AppNotification? _checkLowBalanceCondition(DashboardState dashboardState) {
    try {
      // Use existing dashboard calculation method
      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Check if condition is met (< 24 hours to zero but not already zero)
      if (daysToZero == null || daysToZero >= 1.0 || daysToZero <= 0) {
        return null;
      }

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours.'
            : 'Your meter balance is critically low.',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('NotificationEvaluator: Error checking low balance: $e');
      return null;
    }
  }

  /// Check time to top up condition using dashboard calculation
  AppNotification? _checkTimeToTopUpCondition(
    DashboardState dashboardState,
    double alertThreshold,
    int daysInAdvance,
  ) {
    try {
      // Use existing dashboard calculation method
      final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
        alertThreshold,
        daysInAdvance,
      );

      // Check if condition is met (< 24 hours to threshold)
      if (daysToThreshold == null || daysToThreshold >= 1.0) {
        return null;
      }

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error('NotificationEvaluator: Error checking time to top up: $e');
      return null;
    }
  }

  /// Force re-evaluation of conditions
  void refresh() {
    ref.invalidateSelf();
  }
}
