import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../../core/shared/enums/entry_enums.dart';

part 'history_state.freezed.dart';

/// Immutable state for the History feature
@freezed
class HistoryState with _$HistoryState {
  const factory HistoryState({
    /// List of entries (both meter readings and top-ups)
    @Default([]) List<dynamic> entries,

    /// Current page
    @Default(0) int currentPage,

    /// Total number of pages
    @Default(1) int totalPages,

    /// Entries per page
    @Default(20) int entriesPerPage,

    /// Current filter type
    @Default(EntryFilterType.all) EntryFilterType filterType,

    /// Current sort order
    @Default(EntrySortOrder.newestFirst) EntrySortOrder sortOrder,

    /// Date range filter - start date
    DateTime? startDate,

    /// Date range filter - end date
    DateTime? endDate,

    /// Loading state
    @Default(false) bool isLoading,

    /// Error message
    String? errorMessage,

    /// Total count of all entries (for pagination calculation)
    @Default(0) int totalEntries,

    /// Whether pagination controls should be visible
    @Default(false) bool showPaginationControls,
  }) = _HistoryState;

  /// Initial history state
  factory HistoryState.initial() => const HistoryState();
}

/// Extension methods for HistoryState
extension HistoryStateX on HistoryState {
  /// Check if there are entries to display
  bool get hasEntries => entries.isNotEmpty;

  /// Check if there's an error
  bool get hasError => errorMessage != null;

  /// Check if pagination is needed
  bool get needsPagination => totalPages > 1;

  /// Check if can go to next page
  bool get canGoNext => currentPage < totalPages - 1;

  /// Check if can go to previous page
  bool get canGoPrevious => currentPage > 0;

  /// Get current page display (1-based)
  int get displayPage => currentPage + 1;

  /// Check if using date range filter
  bool get hasDateFilter => startDate != null && endDate != null;

  /// Get meter readings from entries
  List<MeterReading> get meterReadings =>
      entries.whereType<MeterReading>().toList();

  /// Get top-ups from entries
  List<TopUp> get topUps => entries.whereType<TopUp>().toList();

  /// Get invalid entries
  List<dynamic> get invalidEntries => entries.where((entry) {
        if (entry is MeterReading) return !entry.isValid;
        if (entry is TopUp) return !entry.isValid;
        return false;
      }).toList();
}
