import 'dart:async';
import 'logger.dart';

/// Singleton lock to prevent concurrent average calculations
class AverageCalculationLock {
  static final AverageCalculationLock _instance =
      AverageCalculationLock._internal();
  factory AverageCalculationLock() => _instance;
  AverageCalculationLock._internal();

  bool _isLocked = false;
  final List<Completer<void>> _waitingQueue = [];
  Timer? _debounceTimer;
  final List<Future<void> Function()> _pendingCalculations = [];

  /// Debounce timeout in milliseconds
  static const int _debounceTimeoutMs = 500;

  /// Check if calculation is currently in progress
  bool get isLocked => _isLocked;

  /// Get number of pending calculations
  int get pendingCount => _pendingCalculations.length;

  /// Execute calculation with lock protection and debouncing
  Future<void> executeWithLock(Future<void> Function() calculation) async {
    // Add to pending calculations for debouncing
    _pendingCalculations.add(() async {
      await calculation();
    });

    // Cancel existing debounce timer
    _debounceTimer?.cancel();

    // Start new debounce timer
    _debounceTimer =
        Timer(const Duration(milliseconds: _debounceTimeoutMs), () async {
      await _executePendingCalculations();
    });
  }

  /// Execute all pending calculations as a single batch
  Future<void> _executePendingCalculations() async {
    if (_pendingCalculations.isEmpty) return;

    // Wait for lock if another calculation is in progress
    await _waitForLock();

    try {
      _isLocked = true;
      final calculationCount = _pendingCalculations.length;

      Logger.info(
          'AverageCalculationLock: Starting batch calculation of $calculationCount requests');

      // Execute the most recent calculation (others are redundant)
      if (_pendingCalculations.isNotEmpty) {
        final lastCalculation = _pendingCalculations.last;
        await lastCalculation();
      }

      // Clear all pending calculations
      _pendingCalculations.clear();

      Logger.info('AverageCalculationLock: Completed batch calculation');
    } catch (e) {
      Logger.error('AverageCalculationLock: Error during calculation: $e');
      _pendingCalculations.clear();
      rethrow;
    } finally {
      _isLocked = false;
      _notifyWaitingQueue();
    }
  }

  /// Wait for current calculation to complete
  Future<void> _waitForLock() async {
    if (!_isLocked) return;

    final completer = Completer<void>();
    _waitingQueue.add(completer);

    Logger.info(
        'AverageCalculationLock: Waiting for lock (${_waitingQueue.length} in queue)');

    return completer.future;
  }

  /// Notify all waiting calculations that lock is available
  void _notifyWaitingQueue() {
    while (_waitingQueue.isNotEmpty) {
      final completer = _waitingQueue.removeAt(0);
      if (!completer.isCompleted) {
        completer.complete();
      }
    }
  }

  /// Reset lock state (for testing or error recovery)
  void reset() {
    _debounceTimer?.cancel();
    _isLocked = false;
    _pendingCalculations.clear();

    // Complete any waiting operations
    _notifyWaitingQueue();

    Logger.info('AverageCalculationLock: Reset lock state');
  }
}
