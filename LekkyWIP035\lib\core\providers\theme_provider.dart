import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../constants/preference_keys.dart';
import '../shared/models/theme_mode.dart';
import '../models/theme_state.dart';

part 'theme_provider.g.dart';

/// Comprehensive theme provider using Riverpod with AppColors integration
@riverpod
class Theme extends _$Theme {
  @override
  Future<ThemeState> build() async {
    return await _loadTheme();
  }

  /// Load theme from SharedPreferences
  Future<ThemeState> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(PreferenceKeys.themeMode);

      AppThemeMode themeMode = AppThemeMode.system;
      if (themeModeString != null) {
        try {
          themeMode = AppThemeMode.values.firstWhere(
            (mode) => mode.toString() == themeModeString,
            orElse: () => AppThemeMode.system,
          );
        } catch (e) {
          themeMode = AppThemeMode.system;
        }
      }

      return ThemeState(
        themeMode: themeMode,
        isDarkMode: _isDarkMode(themeMode),
      );
    } catch (e) {
      // Return default theme on error
      return const ThemeState();
    }
  }

  /// Determine if dark mode should be active
  bool _isDarkMode(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        // Check system brightness for system mode
        try {
          final window = WidgetsBinding.instance.platformDispatcher;
          return window.platformBrightness == Brightness.dark;
        } catch (e) {
          // Fallback to light mode if system brightness cannot be determined
          return false;
        }
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode mode) async {
    final currentState = await future;
    if (currentState.themeMode == mode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.themeMode, mode.toString());

      // Theme changes applied directly through this provider

      // Update state
      state = AsyncValue.data(ThemeState(
        themeMode: mode,
        isDarkMode: _isDarkMode(mode),
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final currentState = await future;
    final newMode = currentState.themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    await updateThemeMode(newMode);
  }

  /// Set system theme mode
  Future<void> setSystemTheme() async {
    await updateThemeMode(AppThemeMode.system);
  }

  /// Set light theme mode
  Future<void> setLightTheme() async {
    await updateThemeMode(AppThemeMode.light);
  }

  /// Set dark theme mode
  Future<void> setDarkTheme() async {
    await updateThemeMode(AppThemeMode.dark);
  }

  /// Get current Flutter ThemeMode for MaterialApp
  ThemeMode get currentFlutterThemeMode {
    final currentState = state.value;
    if (currentState == null) return ThemeMode.system;

    switch (currentState.themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Refresh theme state (useful for system theme changes)
  Future<void> refreshTheme() async {
    try {
      final newState = await _loadTheme();
      state = AsyncValue.data(newState);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
