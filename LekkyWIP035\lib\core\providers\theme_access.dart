import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'theme_provider.dart';
import '../models/theme_state.dart';

/// Extension on WidgetRef for convenient theme access
extension ThemeAccess on WidgetRef {
  /// Get current theme state
  AsyncValue<ThemeState> get themeAsync => watch(themeProvider);
  
  /// Get current theme state (synchronous, may be null)
  ThemeState? get theme => watch(themeProvider).value;
  
  /// Get dashboard gradient colors
  List<Color>? get dashboardGradient => theme?.dashboardMainCardGradient;
  
  /// Get cost gradient colors
  List<Color>? get costGradient => theme?.costMainCardGradient;
  
  /// Get history gradient colors
  List<Color>? get historyGradient => theme?.historyMainCardGradient;
  
  /// Get settings gradient colors
  List<Color>? get settingsGradient => theme?.settingsMainCardGradient;
  
  /// Get notification gradient colors
  List<Color>? get notificationGradient => theme?.notificationMainCardGradient;
  
  /// Get app bar text color for specific screen
  Color? getAppBarTextColor(String screen) => theme?.getAppBarTextColor(screen);
  
  /// Get dashboard meter text color
  Color? get dashboardMeterTextColor => theme?.dashboardMeterTextColor;
  
  /// Get dashboard top-up text color
  Color? get dashboardTopUpTextColor => theme?.dashboardTopUpTextColor;
  
  /// Check if dark mode is active
  bool get isDarkMode => theme?.isDarkMode ?? false;
  
  /// Get theme mode display name
  String get themeModeDisplayName => theme?.themeModeDisplayName ?? 'System';
}

/// Extension on ConsumerWidget for convenient theme access
extension ConsumerThemeAccess on ConsumerWidget {
  /// Get theme state from ref
  ThemeState? getTheme(WidgetRef ref) => ref.theme;
  
  /// Get dashboard gradient from ref
  List<Color>? getDashboardGradient(WidgetRef ref) => ref.dashboardGradient;
  
  /// Get cost gradient from ref
  List<Color>? getCostGradient(WidgetRef ref) => ref.costGradient;
  
  /// Get history gradient from ref
  List<Color>? getHistoryGradient(WidgetRef ref) => ref.historyGradient;
  
  /// Get settings gradient from ref
  List<Color>? getSettingsGradient(WidgetRef ref) => ref.settingsGradient;
}

/// Helper class for theme-related utilities
class ThemeHelper {
  /// Create a gradient from color list
  static LinearGradient createGradient(List<Color> colors) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: colors,
    );
  }
  
  /// Create a gradient with fallback colors
  static LinearGradient createGradientWithFallback(
    List<Color>? colors, {
    List<Color>? fallbackColors,
  }) {
    final gradientColors = colors ?? 
        fallbackColors ?? 
        [Colors.blue.shade400, Colors.blue.shade600];
    
    return createGradient(gradientColors);
  }
  
  /// Get screen-specific gradient with fallback
  static LinearGradient getScreenGradient(
    WidgetRef ref, 
    String screen, {
    List<Color>? fallbackColors,
  }) {
    List<Color>? colors;
    
    switch (screen.toLowerCase()) {
      case 'dashboard':
      case 'home':
        colors = ref.dashboardGradient;
        break;
      case 'cost':
        colors = ref.costGradient;
        break;
      case 'history':
        colors = ref.historyGradient;
        break;
      case 'settings':
        colors = ref.settingsGradient;
        break;
      case 'notification':
        colors = ref.notificationGradient;
        break;
    }
    
    return createGradientWithFallback(colors, fallbackColors: fallbackColors);
  }
}
