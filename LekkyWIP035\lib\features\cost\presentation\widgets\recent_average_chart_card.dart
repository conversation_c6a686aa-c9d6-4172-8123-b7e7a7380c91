// File: lib/features/cost/presentation/widgets/recent_average_chart_card.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/cost_trend_chart.dart';
import '../models/chart_data.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/models/preference_state.dart';
import '../../../../core/providers/date_formatter_provider.dart';

/// Widget that displays recent average cost trend over time
class RecentAverageChartCard extends ConsumerWidget {
  /// Chart data to display
  final List<ChartData> chartData;

  /// Currency symbol for display
  final String currencySymbol;

  /// User preferences for formatting
  final PreferenceState preferences;

  /// Date range information for display
  final String? dateRangeText;

  /// Constructor
  const RecentAverageChartCard({
    super.key,
    required this.chartData,
    required this.currencySymbol,
    required this.preferences,
    this.dateRangeText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: AppColors.getRecentAverageChartBackground(isDark),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: theme.colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Average Trend',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Chart or empty state
            SizedBox(
              height: 200,
              child: chartData.isEmpty
                  ? _buildEmptyState(context)
                  : CostTrendChart(
                      data: chartData,
                      meterUnit: currencySymbol,
                      preferences: preferences,
                      showCost: true, // Show cost per day
                    ),
            ),

            // Data info
            if (chartData.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildDataInfo(context, ref),
            ],
          ],
        ),
      ),
    );
  }

  /// Build empty state when no data available
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 48,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          const SizedBox(height: 12),
          Text(
            'No data available',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Add meter readings to see recent average trends',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build data information row
  Widget _buildDataInfo(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final dataPointCount = chartData.length;
    final dateRange = dateRangeText ?? _getDateRangeFromData(ref);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$dataPointCount data points',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            fontStyle: FontStyle.italic,
          ),
        ),
        if (dateRange.isNotEmpty)
          Text(
            dateRange,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontStyle: FontStyle.italic,
            ),
          ),
      ],
    );
  }

  /// Get date range text from chart data
  String _getDateRangeFromData(WidgetRef ref) {
    if (chartData.isEmpty) return '';

    final firstDate = chartData.first.date;
    final lastDate = chartData.last.date;

    if (firstDate.year == lastDate.year) {
      return '${_formatDate(firstDate, ref)} - ${_formatDate(lastDate, ref)}';
    } else {
      return '${_formatDateWithYear(firstDate, ref)} - ${_formatDateWithYear(lastDate, ref)}';
    }
  }

  /// Format date without year
  String _formatDate(DateTime date, WidgetRef ref) {
    return ref.watch(dateFormatterProvider).formatDateForChart(date);
  }

  /// Format date with year
  String _formatDateWithYear(DateTime date, WidgetRef ref) {
    return ref.watch(dateFormatterProvider).formatDateForChartWithYear(date);
  }
}
