import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// Represents different types of errors that can occur in the app
enum AppErrorType {
  /// Network-related errors
  network,
  
  /// Database-related errors
  database,
  
  /// Validation errors
  validation,
  
  /// Permission errors
  permission,
  
  /// File system errors
  fileSystem,
  
  /// Unknown/unexpected errors
  unknown,
}

/// Immutable error model for consistent error handling across the app
@freezed
class AppError with _$AppError {
  const factory AppError({
    required AppErrorType type,
    required String message,
    String? details,
    String? code,
    @Default(false) bool isRetryable,
  }) = _AppError;

  /// Create an AppError from an exception
  factory AppError.fromException(Object exception, {String? code}) {
    if (exception is AppError) {
      return exception;
    }

    String message = exception.toString();
    AppErrorType type = AppErrorType.unknown;
    bool isRetryable = false;

    // Categorize common exceptions
    if (exception.toString().contains('SocketException') ||
        exception.toString().contains('TimeoutException')) {
      type = AppErrorType.network;
      isRetryable = true;
      message = 'Network connection failed. Please check your internet connection.';
    } else if (exception.toString().contains('DatabaseException') ||
               exception.toString().contains('SQLite')) {
      type = AppErrorType.database;
      isRetryable = true;
      message = 'Database operation failed. Please try again.';
    } else if (exception.toString().contains('Permission')) {
      type = AppErrorType.permission;
      isRetryable = false;
      message = 'Permission denied. Please check app permissions.';
    } else if (exception.toString().contains('FileSystemException')) {
      type = AppErrorType.fileSystem;
      isRetryable = true;
      message = 'File operation failed. Please try again.';
    }

    return AppError(
      type: type,
      message: message,
      details: exception.toString(),
      code: code,
      isRetryable: isRetryable,
    );
  }

  /// Create a validation error
  factory AppError.validation(String message, {String? field}) {
    return AppError(
      type: AppErrorType.validation,
      message: message,
      details: field != null ? 'Field: $field' : null,
      isRetryable: false,
    );
  }

  /// Create a network error
  factory AppError.network(String message) {
    return AppError(
      type: AppErrorType.network,
      message: message,
      isRetryable: true,
    );
  }

  /// Create a database error
  factory AppError.database(String message) {
    return AppError(
      type: AppErrorType.database,
      message: message,
      isRetryable: true,
    );
  }
}
