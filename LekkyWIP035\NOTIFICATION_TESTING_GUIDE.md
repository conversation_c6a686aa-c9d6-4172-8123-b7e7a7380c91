# Lekky Notification System Testing Guide

## Overview
This guide provides comprehensive instructions for testing the newly implemented notification system in the Lekky app.

## Quick Start Testing

### 1. Access Debug Screen
Navigate to the notification debug screen:
```
Route: /debug/notifications
```
Or add a temporary button in your UI to navigate there.

### 2. Basic Functionality Test
1. Open the debug screen
2. Tap "Request Permissions" to ensure notification permissions are granted
3. Tap "Test Welcome Notification" to verify basic notification functionality
4. Check your device's notification panel for the test notification

### 3. Alert System Test
1. Tap "Run Full Alert Check" to test the complete alert system
2. Check the status report for any errors
3. Verify that alerts respect current settings (enabled/disabled)

## Detailed Testing Procedures

### Testing Notification Types

#### Low Balance Alerts
**Purpose**: Notify when meter will reach zero within 24 hours

**Test Steps**:
1. Enable notifications in app settings
2. Enable "Low Balance Alerts" 
3. Use debug screen "Test Low Balance Alert" button
4. Verify notification appears with critical priority (high importance)

**Expected Behavior**:
- Notification uses critical alert channel
- High priority/importance
- Should appear even in Do Not Disturb mode (platform dependent)

#### Threshold Alerts  
**Purpose**: Notify when balance will reach user threshold within specified days

**Test Steps**:
1. Set alert threshold (e.g., £10) in settings
2. Set days in advance (e.g., 3 days) in settings
3. Enable "Time to Top Up Alerts"
4. Use debug screen "Test Threshold Alert" button
5. Verify notification appears with medium priority

**Expected Behavior**:
- Notification uses threshold alert channel
- Medium priority/importance
- Respects user-configured threshold and advance warning period

#### Meter Reading Reminders
**Purpose**: Periodic reminders to take meter readings

**Test Steps**:
1. Enable reminders in settings
2. Set reminder frequency (daily, weekly, etc.)
3. Use debug screen "Test Reminder Notification" button
4. Verify notification appears with normal priority

**Expected Behavior**:
- Notification uses reminder channel
- Normal priority/importance
- Follows configured frequency schedule

### Testing Permission Handling

#### Android 13+ Testing
**Requirements**: Test on Android 13 or higher

**Test Steps**:
1. Fresh app install or clear app data
2. Launch app - should NOT request permissions immediately
3. Enable first notification type in settings
4. Should trigger permission request dialog
5. Test both "Allow" and "Deny" scenarios

**Expected Behavior**:
- Permissions only requested when first notification enabled
- Graceful handling of denied permissions
- Fallback to in-app alerts when permissions denied

#### iOS Testing
**Requirements**: Test on iOS device

**Test Steps**:
1. Fresh app install
2. Enable notifications in settings
3. Should trigger iOS permission dialog
4. Test "Allow", "Don't Allow", and partial permissions

**Expected Behavior**:
- Standard iOS permission flow
- Handles partial permissions (e.g., alerts but no sounds)
- Graceful degradation when permissions limited

### Testing Background Monitoring

#### Background Alert Checks
**Purpose**: Verify alerts work when app is closed

**Test Steps**:
1. Enable notifications and set up test conditions
2. Close app completely (not just minimize)
3. Wait 6+ hours or manually trigger background task
4. Reopen app
5. Check for any missed alerts or background flags

**Expected Behavior**:
- Background service runs every 6 hours
- Sets flags for foreground processing
- Alerts fire when app resumes if conditions met

#### App Lifecycle Testing
**Test Steps**:
1. Enable notifications
2. Minimize app (background)
3. Resume app
4. Check debug screen for background alert requests
5. Verify any pending alerts are processed

**Expected Behavior**:
- App checks for background requests on resume
- Processes any pending alerts immediately
- Clears background flags after processing

### Testing Error Handling

#### Network/Service Failures
**Test Steps**:
1. Enable airplane mode
2. Try to fire notifications
3. Check for fallback behavior
4. Re-enable network and test recovery

**Expected Behavior**:
- Graceful handling of service failures
- Retry logic attempts 3 times
- Falls back to in-app alerts
- Recovers when service restored

#### Permission Denied Scenarios
**Test Steps**:
1. Deny notification permissions
2. Enable alerts in app settings
3. Trigger alert conditions
4. Verify fallback to in-app alerts

**Expected Behavior**:
- No crashes when permissions denied
- In-app alert dialogs appear instead
- User can still receive critical information

### Testing Deduplication

#### Same-Day Alert Prevention
**Test Steps**:
1. Enable low balance alerts
2. Fire test notification
3. Immediately try to fire same type again
4. Verify second notification is blocked

**Expected Behavior**:
- Only one notification per type per day
- Prevents notification spam
- Resets at midnight for next day

#### Data Change Triggers
**Test Steps**:
1. Fire alert for current conditions
2. Add new meter reading or top-up
3. Verify new alert can fire for changed conditions

**Expected Behavior**:
- New data allows fresh alerts
- System recognizes changed conditions
- Appropriate alerts fire for new state

## Performance Testing

### Battery Impact
**Test Steps**:
1. Enable all notification types
2. Monitor battery usage over 24 hours
3. Check background app refresh settings
4. Verify reasonable battery consumption

**Expected Behavior**:
- Minimal battery impact
- Background tasks respect system limits
- No excessive wake-ups or processing

### Memory Usage
**Test Steps**:
1. Monitor app memory usage with notifications enabled
2. Test with large notification history
3. Verify no memory leaks

**Expected Behavior**:
- Stable memory usage
- Proper cleanup of notification objects
- No accumulating memory leaks

## Troubleshooting Common Issues

### Notifications Not Appearing
**Check List**:
1. Permissions granted? (Use debug screen)
2. Notifications enabled in app settings?
3. Specific alert type enabled?
4. Device Do Not Disturb settings?
5. App notification settings in system settings?

### Background Monitoring Not Working
**Check List**:
1. Background app refresh enabled?
2. Battery optimization disabled for app?
3. Auto-start permissions granted (Android)?
4. Check background flags in debug screen

### In-App Alerts Not Showing
**Check List**:
1. Check debug screen for pending alerts
2. Verify app lifecycle handling
3. Check for dialog conflicts
4. Ensure proper context available

## Debug Tools Usage

### Status Report
- Shows complete system state
- Identifies configuration issues
- Displays recent notification activity
- Reveals permission status

### Test Notifications
- Verifies basic notification functionality
- Tests each notification type independently
- Confirms channel configuration
- Validates permission handling

### Clear All Data
- Resets notification system to clean state
- Useful for testing fresh install scenarios
- Clears all preferences and scheduled notifications
- **Warning**: This is destructive - use carefully

## Production Deployment Checklist

### Pre-Release Testing
- [ ] Test on multiple Android versions (especially 13+)
- [ ] Test on iOS devices
- [ ] Verify all notification types work
- [ ] Test permission flows
- [ ] Verify background monitoring
- [ ] Test error handling scenarios
- [ ] Validate battery impact
- [ ] Check notification appearance/styling

### Configuration Verification
- [ ] Notification icons present in resources
- [ ] Android notification channels configured
- [ ] iOS permissions in Info.plist
- [ ] Background execution permissions set
- [ ] Default settings appropriate (all OFF)

### Monitoring Setup
- [ ] Logging enabled for notification events
- [ ] Error tracking for notification failures
- [ ] Analytics for permission grant rates
- [ ] User feedback collection mechanism

## Known Limitations

### Platform Constraints
- **Android**: Background execution limited by battery optimization
- **iOS**: Background app refresh must be enabled
- **Both**: System can kill background tasks under memory pressure

### Design Decisions
- Deduplication prevents spam but may delay urgent alerts
- Background monitoring every 6 hours balances battery vs responsiveness
- In-app fallbacks ensure critical alerts reach users

### Future Enhancements
- Push notifications for server-driven alerts
- More granular notification scheduling
- Advanced user customization options
- Analytics and effectiveness tracking

## Support and Debugging

### Log Analysis
Check logs for these key events:
- `NotificationService initialized successfully`
- `UnifiedAlertManager: Successfully fired [type] notification`
- `ReactiveAlertListener: Processing background alert request`
- `Background monitoring started (6-hour intervals)`

### Common Error Messages
- `Notification plugin initialization returned false` - Check permissions
- `Failed to fire test notification` - Check service configuration
- `No notification permissions` - User needs to grant permissions
- `No dashboard state available` - Check data availability

### Getting Help
1. Generate status report from debug screen
2. Check device logs for error messages
3. Verify configuration against this guide
4. Test with debug tools to isolate issues

Remember: The notification system is designed to fail gracefully. Even if notifications don't work, the app should continue functioning normally with in-app alerts as fallback.
