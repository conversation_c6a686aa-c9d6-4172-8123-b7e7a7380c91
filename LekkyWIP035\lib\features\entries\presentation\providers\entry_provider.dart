// File: lib/features/entries/presentation/providers/entry_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../validation/domain/services/validation_trigger_service.dart';
import '../../domain/models/entry_state.dart';
import '../controllers/entry_controller.dart';

/// Entry provider for managing entry state with Riverpod
class EntryNotifier extends StateNotifier<EntryState> {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final logger = Logger('EntryNotifier');

  EntryNotifier(this._meterReadingRepository, this._topUpRepository)
      : super(EntryState.initial());

  /// Set the entry type
  void setEntryType(EntryType type) {
    state = state.copyWith(entryType: type);
  }

  /// Set the date and time
  void setDateTime(DateTime dateTime) {
    state = state.copyWith(dateTime: dateTime);
    _validateDateTime();
  }

  /// Set the value/amount
  void setValue(double value) {
    state = state.copyWith(value: value);
    _validateValue();
  }

  /// Set the notes
  void setNotes(String notes) {
    state = state.copyWith(notes: notes);
  }

  /// Reset the controller to default values
  void reset() {
    state = EntryState.initial();
  }

  /// Initialize with an existing meter reading
  void initWithMeterReading(MeterReading meterReading) {
    state = EntryState.fromMeterReading(meterReading);
  }

  /// Initialize with an existing top-up
  void initWithTopUp(TopUp topUp) {
    state = EntryState.fromTopUp(topUp);
  }

  /// Validate the current entry
  bool validate() {
    final validationErrors = <String, String>{};

    // Validate date time
    if (state.dateTime.isAfter(DateTime.now())) {
      validationErrors['dateTime'] = 'Date cannot be in the future';
    }

    // Validate value
    if (state.value < 0) {
      validationErrors['value'] = state.entryType == EntryType.meterReading
          ? 'Meter Reading must be a positive number'
          : 'Top-up amount must be a positive number';
    }

    state = state.copyWith(validationErrors: validationErrors);
    return validationErrors.isEmpty;
  }

  /// Validate the date and time
  void _validateDateTime() {
    final validationErrors = Map<String, String>.from(state.validationErrors);

    if (state.dateTime.isAfter(DateTime.now())) {
      validationErrors['dateTime'] = 'Date cannot be in the future';
    } else {
      validationErrors.remove('dateTime');
    }

    state = state.copyWith(validationErrors: validationErrors);
  }

  /// Validate the value/amount
  void _validateValue() {
    final validationErrors = Map<String, String>.from(state.validationErrors);

    if (state.value < 0) {
      validationErrors['value'] = state.entryType == EntryType.meterReading
          ? 'Meter Reading must be a positive number'
          : 'Top-up amount must be a positive number';
    } else {
      validationErrors.remove('value');
    }

    state = state.copyWith(validationErrors: validationErrors);
  }

  /// Save the current entry
  Future<bool> saveEntry() async {
    if (!validate()) {
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      if (state.entryType == EntryType.meterReading) {
        await _saveMeterReading();
      } else {
        await _saveTopUp();
      }

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to save entry: ${e.toString()}',
      );
      logger.e('EntryNotifier saveEntry error: $e');
      return false;
    }
  }

  /// Save meter reading
  Future<void> _saveMeterReading() async {
    final meterReading = MeterReading(
      value: state.value,
      date: state.dateTime,
      status: EntryStatus.valid,
      notes: state.notes.isNotEmpty ? state.notes : null,
    );

    await _meterReadingRepository.addMeterReading(meterReading);
  }

  /// Save top-up
  Future<void> _saveTopUp() async {
    final topUp = TopUp(
      amount: state.value,
      date: state.dateTime,
      notes: state.notes.isNotEmpty ? state.notes : null,
    );

    await _topUpRepository.addTopUp(topUp);
  }

  /// Update an existing meter reading
  Future<bool> updateMeterReading(int id) async {
    if (!validate()) {
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      final meterReading = MeterReading(
        id: id,
        value: state.value,
        date: state.dateTime,
        status: EntryStatus.valid,
        notes: state.notes.isNotEmpty ? state.notes : null,
      );

      await _meterReadingRepository.updateMeterReading(meterReading);

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to update meter reading: ${e.toString()}',
      );
      logger.e('EntryNotifier updateMeterReading error: $e');
      return false;
    }
  }

  /// Update an existing top-up
  Future<bool> updateTopUp(int id) async {
    if (!validate()) {
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      final topUp = TopUp(
        id: id,
        amount: state.value,
        date: state.dateTime,
        notes: state.notes.isNotEmpty ? state.notes : null,
      );

      await _topUpRepository.updateTopUp(topUp);

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to update top-up: ${e.toString()}',
      );
      logger.e('EntryNotifier updateTopUp error: $e');
      return false;
    }
  }

  /// Convert a meter reading to a top-up
  Future<bool> convertMeterReadingToTopUp(int meterReadingId) async {
    if (!validate()) {
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      final topUp = TopUp(
        amount: state.value,
        date: state.dateTime,
        notes: state.notes.isNotEmpty ? state.notes : null,
      );

      final addedTopUpId = await _topUpRepository.addTopUp(topUp);
      await _meterReadingRepository.deleteMeterReading(meterReadingId);

      // Trigger validation for the conversion
      _triggerConversionValidationAsync(addedTopUpId, state.dateTime);

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to convert entry: ${e.toString()}',
      );
      logger.e('EntryNotifier convertMeterReadingToTopUp error: $e');
      return false;
    }
  }

  /// Convert a top-up to a meter reading
  Future<bool> convertTopUpToMeterReading(int topUpId) async {
    if (!validate()) {
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      final meterReading = MeterReading(
        value: state.value,
        date: state.dateTime,
        status: EntryStatus.valid,
        notes: state.notes.isNotEmpty ? state.notes : null,
      );

      final addedReadingId =
          await _meterReadingRepository.addMeterReading(meterReading);
      await _topUpRepository.deleteTopUp(topUpId);

      // Trigger validation for the conversion
      _triggerConversionValidationAsync(addedReadingId, state.dateTime);

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to convert entry: ${e.toString()}',
      );
      logger.e('EntryNotifier convertTopUpToMeterReading error: $e');
      return false;
    }
  }

  /// Trigger validation asynchronously for conversions
  void _triggerConversionValidationAsync(int entryId, DateTime entryDate) {
    try {
      final validationTriggerService =
          serviceLocator<ValidationTriggerService>();
      validationTriggerService.validateAfterEntryConversion(entryDate);
    } catch (e) {
      logger.w('Failed to trigger validation for conversion: $e');
    }
  }
}

/// Provider for entry management
final entryProvider = StateNotifierProvider<EntryNotifier, EntryState>((ref) {
  final meterReadingRepository = serviceLocator<MeterReadingRepository>();
  final topUpRepository = serviceLocator<TopUpRepository>();
  return EntryNotifier(meterReadingRepository, topUpRepository);
});
