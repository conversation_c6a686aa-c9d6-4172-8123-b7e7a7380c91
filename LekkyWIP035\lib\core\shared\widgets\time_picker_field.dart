import 'package:flutter/material.dart';

/// A widget for selecting a time
class TimePicker<PERSON>ield extends StatelessWidget {
  /// Selected time
  final TimeOfDay selectedTime;
  
  /// Callback when time changes
  final Function(TimeOfDay) onTimeChanged;
  
  /// Label for the field
  final String label;
  
  /// Whether the field is disabled
  final bool isDisabled;
  
  /// Constructor
  const TimePickerField({
    super.key,
    required this.selectedTime,
    required this.onTimeChanged,
    this.label = 'Reminder Time',
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: isDisabled ? 0.5 : 1.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: isDisabled ? null : () => _showTimePicker(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedTime.format(context),
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                      fontSize: 16,
                    ),
                  ),
                  Icon(
                    Icons.access_time,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Future<void> _showTimePicker(BuildContext context) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: selectedTime,
    );
    
    if (pickedTime != null && pickedTime != selectedTime) {
      onTimeChanged(pickedTime);
    }
  }
}
