# Standardized Terminology for NewLekky App

This document defines the standard terminology to be used consistently throughout the NewLekky app, its documentation, and codebase.

## Core Concepts

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Meter Reading** | A recorded value from the electricity meter | Always use "Meter Reading" (not just "Reading" or "Entry") |
| **Top-up** | An amount added to the electricity meter | Always hyphenated as "Top-up" (not "Top Up" or "Topup") |
| **Entry** | Generic term for either a Meter Reading or Top-up record | Use when referring to both types collectively |
| **Recent Average** | Average daily usage calculated from recent readings | Use "Recent Average" (not "Short-term Average") |
| **Total Average** | Average daily usage calculated from all readings | Use "Total Average" (not "Long-term Average") |
| **Days Remaining** | Projected days until meter reaches zero | Use "Days Remaining" (not "Days Left" or "Remaining Days") |

## Data Validation Terms

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Invalid Entry** | An entry that fails validation rules | Use for any entry that doesn't meet validation criteria |
| **Warning** | A potential issue that doesn't prevent saving | Use for conditions that warrant user attention but aren't errors |
| **Error** | A critical issue that prevents saving | Use for conditions that must be resolved before proceeding |
| **Validation Rule** | A specific criterion that entries must meet | Use when referring to individual validation requirements |

## UI Component Terms

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Entry Card** | UI component displaying an entry in a list | Use consistently for this component across all screens |
| **Meter Status Card** | UI component showing current meter status | Use on Home screen for the main status display |
| **Add Entry Dialog** | Dialog for creating a new entry | Use consistently for this dialog across all screens |
| **Edit Entry Dialog** | Dialog for modifying an existing entry | Use consistently for this dialog across all screens |
| **Delete Confirmation Dialog** | Dialog confirming entry deletion | Use consistently for this dialog across all screens |
| **Message Bar** | UI component for displaying notifications | Use for the notification area at the top of screens |

## Feature Terms

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **History** | Screen showing all entries | Use for the screen displaying the entry list |
| **Cost** | Screen showing cost calculations | Use for the screen displaying cost information |
| **Settings** | Screen for app configuration | Use for the screen with user preferences |
| **Filter** | Function to narrow down displayed entries | Use for the filtering functionality in History |
| **Sort** | Function to order displayed entries | Use for the sorting functionality in History |
| **Export** | Function to save data externally | Use for the backup functionality |
| **Import** | Function to load external data | Use for the restore functionality |

## Notification Terms

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Low Balance Alert** | Notification when balance is low | Use for alerts about low meter balance |
| **Time to Top-up Alert** | Notification when top-up is needed soon | Use for alerts about upcoming top-up need |
| **Invalid Entry Alert** | Notification about invalid entries | Use for alerts about validation issues |
| **Reading Reminder** | Scheduled notification to take a reading | Use for scheduled reminders |

## Technical Terms

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Provider** | State management component | Use when referring to Riverpod providers |
| **Repository** | Data access component | Use when referring to data access layer |
| **Service** | Business logic component | Use when referring to service layer |
| **Model** | Data structure | Use when referring to data models |
| **Widget** | UI component | Use when referring to Flutter widgets |

## Date and Number Formatting

| Term | Definition | Usage Notes |
|------|------------|-------------|
| **Date Format** | How dates are displayed | Use consistent format based on user preference |
| **Time Format** | How times are displayed | Use consistent format based on user preference |
| **Currency Symbol** | Symbol for monetary values | Use £ symbol consistently for currency |
| **Decimal Separator** | Symbol for decimal point | Use consistent separator based on locale |

## Consistency Guidelines

1. Always use the standardized terms in code, comments, UI text, and documentation
2. Maintain consistent capitalization as defined in this document
3. Use full terms rather than abbreviations in user-facing text
4. When creating new terms, add them to this document for future reference
5. Review terminology usage during code reviews to ensure consistency
