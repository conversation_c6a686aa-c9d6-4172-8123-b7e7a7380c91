import 'package:freezed_annotation/freezed_annotation.dart';
import 'app_error.dart';

part 'error_state.freezed.dart';

/// Global error state for the application
@freezed
class ErrorState with _$ErrorState {
  const factory ErrorState({
    /// Current error being displayed
    AppError? currentError,
    
    /// Whether to show error dialog
    @Default(false) bool showErrorDialog,
    
    /// Whether to show error banner
    @Default(false) bool showErrorBanner,
    
    /// List of recent errors for debugging
    @Default([]) List<AppError> recentErrors,
    
    /// Whether error reporting is enabled
    @Default(true) bool errorReportingEnabled,
  }) = _ErrorState;

  /// Initial error state
  factory ErrorState.initial() => const ErrorState();
}

/// Extension methods for ErrorState
extension ErrorStateX on ErrorState {
  /// Check if there's an active error
  bool get hasError => currentError != null;
  
  /// Check if the current error is retryable
  bool get canRetry => currentError?.isRetryable ?? false;
  
  /// Get user-friendly error message
  String get displayMessage => currentError?.message ?? 'An unexpected error occurred';
  
  /// Check if error should be shown to user
  bool get shouldShowToUser => hasError && (showErrorDialog || showErrorBanner);
}
