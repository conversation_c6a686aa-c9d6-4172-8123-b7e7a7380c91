import 'package:flutter/material.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/di/service_locator.dart';
import '../../../averages/domain/services/average_service.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';

/// Controller for the Home screen
class HomeController extends ChangeNotifier {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  /// Latest meter reading
  MeterReading? _latestMeterReading;

  /// Recent average daily usage
  double? _recentAverageDailyUsage;

  /// Total average daily usage
  double? _totalAverageDailyUsage;

  /// Recent entries (both meter readings and top-ups)
  List<dynamic> _recentEntries = [];

  /// Total top-ups after latest meter reading
  double _totalTopUpsAfterLatestReading = 0.0;

  /// Loading state
  bool _isLoading = true;

  /// Error message
  String? _errorMessage;

  /// Constructor
  HomeController({
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
  })  : _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository;

  /// Get the latest meter reading
  MeterReading? get latestMeterReading => _latestMeterReading;

  /// Get the recent average daily usage
  double? get recentAverageDailyUsage => _recentAverageDailyUsage;

  /// Get the total average daily usage
  double? get totalAverageDailyUsage => _totalAverageDailyUsage;

  /// Get recent entries
  List<dynamic> get recentEntries => _recentEntries;

  /// Get total top-ups after latest meter reading
  double get totalTopUpsAfterLatestReading => _totalTopUpsAfterLatestReading;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get errorMessage => _errorMessage;

  /// Initialize the controller
  Future<void> initialize() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Check if this is the first launch by trying to get the latest meter reading
      final hasData = await _checkIfDataExists();

      if (!hasData) {
        // This is likely the first launch, so don't show an error
        _isLoading = false;
        _errorMessage = null;
        notifyListeners();
        return;
      }

      // Use try-catch for each operation to prevent one failure from stopping everything
      try {
        await _loadLatestMeterReading();
      } catch (e) {
        Logger.error('Error loading latest meter reading: $e');
        // Continue with other operations
      }

      try {
        await _loadAverages();
      } catch (e) {
        Logger.error('Error loading averages: $e');
        // Continue with other operations
      }

      try {
        await _loadRecentEntries();
      } catch (e) {
        Logger.error('Error loading recent entries: $e');
        // Continue with other operations
      }

      try {
        await _calculateTopUpsAfterLatestReading();
      } catch (e) {
        Logger.error('Error calculating top-ups after latest reading: $e');
        // Continue with other operations
      }

      _isLoading = false;
      _errorMessage = null;
    } catch (e) {
      _isLoading = false;
      // Log the error but don't set error message to avoid red banner
      Logger.error('HomeController initialization error: $e');
      // Set error message to null to prevent banner from showing
      _errorMessage = null;
    } finally {
      notifyListeners();
    }
  }

  /// Calculate days remaining based on latest reading and recent average
  double? calculateDaysRemaining() {
    if (_latestMeterReading == null ||
        _recentAverageDailyUsage == null ||
        _recentAverageDailyUsage! <= 0) {
      return null;
    }

    return _latestMeterReading!.value / _recentAverageDailyUsage!;
  }

  /// Load the latest meter reading
  Future<void> _loadLatestMeterReading() async {
    try {
      _latestMeterReading =
          await _meterReadingRepository.getLatestMeterReading();
    } catch (e) {
      Logger.error('Failed to load latest meter reading: $e');
      _errorMessage = 'Failed to load latest meter reading';
    }
  }

  /// Load averages using AverageService with fallback
  Future<void> _loadAverages() async {
    try {
      // Try to use AverageService first
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();

      _recentAverageDailyUsage = result.recentAverage;
      _totalAverageDailyUsage = result.totalAverage;

      Logger.info('HomeController: Loaded averages from database');
    } catch (e) {
      Logger.error('Failed to load averages from service: $e');

      // Fallback to direct calculation
      try {
        await _calculateAveragesFallback();
        Logger.info('HomeController: Used fallback calculation');
      } catch (fallbackError) {
        Logger.error('Fallback calculation also failed: $fallbackError');
        _errorMessage = 'Failed to load usage averages';
      }
    }
  }

  /// Calculate averages (fallback method)
  Future<void> _calculateAveragesFallback() async {
    try {
      // Get all meter readings
      final List<MeterReading> readings =
          await _meterReadingRepository.getAllMeterReadings();

      if (readings.length < 2) {
        // Need at least 2 readings to calculate averages
        _recentAverageDailyUsage = null;
        _totalAverageDailyUsage = null;
        return;
      }

      // Sort readings by date (oldest first)
      readings.sort((a, b) => a.date.compareTo(b.date));

      // Get all top-ups
      final List<TopUp> topUps = await _topUpRepository.getAllTopUps();

      // Sort top-ups by date (oldest first)
      topUps.sort((a, b) => a.date.compareTo(b.date));

      // Calculate recent average (last 30 days or last 5 readings, whichever is less)
      final int recentCount = readings.length >= 5 ? 5 : readings.length;
      final List<MeterReading> recentReadings =
          readings.sublist(readings.length - recentCount);

      if (recentReadings.length >= 2) {
        final firstRecentReading = recentReadings.first;
        final lastRecentReading = recentReadings.last;
        final daysBetween =
            lastRecentReading.date.difference(firstRecentReading.date).inDays;

        if (daysBetween > 0) {
          // Calculate total top-ups between first and last recent readings
          double totalRecentTopUps = 0;
          for (var topUp in topUps) {
            if (topUp.date.isAfter(firstRecentReading.date) &&
                topUp.date.isBefore(lastRecentReading.date)) {
              totalRecentTopUps += topUp.amount;
            }
          }

          // Recent average = (first reading - last reading + top-ups) / days
          // Note: prepaid meters go down with usage, so first reading should be higher
          final usage = firstRecentReading.value -
              lastRecentReading.value +
              totalRecentTopUps;
          _recentAverageDailyUsage = usage > 0 ? usage / daysBetween : 0.0;
        }
      }

      // Calculate total average (all readings)
      if (readings.length >= 2) {
        final firstReading = readings.first;
        final lastReading = readings.last;
        final totalDays = lastReading.date.difference(firstReading.date).inDays;

        if (totalDays > 0) {
          // Calculate total top-ups between first and last readings
          double totalTopUps = 0;
          for (var topUp in topUps) {
            if (topUp.date.isAfter(firstReading.date) &&
                topUp.date.isBefore(lastReading.date)) {
              totalTopUps += topUp.amount;
            }
          }

          // Total average = (first reading - last reading + top-ups) / days
          // Note: prepaid meters go down with usage, so first reading should be higher
          final totalUsage =
              firstReading.value - lastReading.value + totalTopUps;
          _totalAverageDailyUsage =
              totalUsage > 0 ? totalUsage / totalDays : 0.0;
        }
      }

      // In a future implementation, we could save the calculated averages to a dedicated repository
    } catch (e) {
      Logger.error('Failed to calculate averages: $e');
    }
  }

  /// Load recent entries (both meter readings and top-ups)
  Future<void> _loadRecentEntries() async {
    try {
      // Get recent meter readings
      final List<MeterReading> readings =
          await _meterReadingRepository.getMeterReadings(
        page: 0,
        pageSize: 5,
      );

      // Get recent top-ups
      final List<TopUp> topUps = await _topUpRepository.getTopUps(
        page: 0,
        pageSize: 5,
      );

      // Combine and sort by date (most recent first)
      _recentEntries = [...readings, ...topUps];
      _recentEntries.sort((a, b) => b.date.compareTo(a.date));

      // Limit to 5 most recent entries
      if (_recentEntries.length > 5) {
        _recentEntries = _recentEntries.sublist(0, 5);
      }
    } catch (e) {
      Logger.error('Failed to load recent entries: $e');
      _errorMessage = 'Failed to load recent activity';
    }
  }

  /// Calculate total top-ups after the latest meter reading
  Future<void> _calculateTopUpsAfterLatestReading() async {
    try {
      _totalTopUpsAfterLatestReading = 0.0;

      if (_latestMeterReading == null) {
        return;
      }

      // Get all top-ups after the latest meter reading date
      final List<TopUp> allTopUps = await _topUpRepository.getAllTopUps();

      // Filter top-ups that occurred after the latest meter reading
      final topUpsAfterReading = allTopUps
          .where((topUp) => topUp.date.isAfter(_latestMeterReading!.date))
          .toList();

      // Calculate total amount
      _totalTopUpsAfterLatestReading =
          topUpsAfterReading.fold(0.0, (sum, topUp) => sum + topUp.amount);
    } catch (e) {
      Logger.error('Failed to calculate top-ups after latest reading: $e');
      _totalTopUpsAfterLatestReading = 0.0;
    }
  }

  /// Refresh data
  Future<void> refresh() async {
    await initialize();
  }

  /// Clear any error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Check if any data exists in the database
  Future<bool> _checkIfDataExists() async {
    try {
      // Try to get the latest meter reading
      final latestReading =
          await _meterReadingRepository.getLatestMeterReading();

      // If we have a reading, we have data
      return latestReading != null;
    } catch (e) {
      Logger.error('Failed to check if data exists: $e');
      return false;
    }
  }
}
