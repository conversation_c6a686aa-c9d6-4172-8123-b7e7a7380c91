import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/constants/preference_keys.dart';

import '../../../notifications/data/notification_service.dart';
import '../../../notifications/domain/models/notification.dart';
import '../../domain/models/alert_state.dart';

/// Reactive alert provider using Riverpod best practices
final alertProvider = AsyncNotifierProvider<AlertNotifier, AlertState>(
  AlertNotifier.new,
);

/// Alert notifier for reactive state management
class AlertNotifier extends AsyncNotifier<AlertState> {
  @override
  Future<AlertState> build() async {
    return await _loadInitialState();
  }

  /// Load initial alert state from preferences
  Future<AlertState> _loadInitialState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final alertThresholdEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final alertThreshold =
          prefs.getDouble(PreferenceKeys.alertThreshold) ?? 10.0;
      final daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 3;

      // Check if any notification type is enabled
      final enabled =
          lowBalanceEnabled || alertThresholdEnabled || invalidRecordEnabled;

      return AlertState(
        enabled: enabled,
        lowBalanceEnabled: lowBalanceEnabled,
        alertThresholdEnabled: alertThresholdEnabled,
        alertThreshold: alertThreshold,
        daysInAdvance: daysInAdvance,
        lastSuccessfulCheck: DateTime.now(),
      );
    } catch (e) {
      Logger.error('Error loading initial alert state: $e');
      return AlertState.initial().copyWith(
        error: AlertError.unknown(details: e.toString()),
        status: AlertStatus.error,
      );
    }
  }

  /// Update alert settings reactively
  Future<void> updateSettings({
    bool? enabled,
    bool? lowBalanceEnabled,
    bool? alertThresholdEnabled,
    double? alertThreshold,
    int? daysInAdvance,
  }) async {
    final currentState = await future;

    // Build changes map for event tracking
    final changes = <String, dynamic>{};
    if (enabled != null && enabled != currentState.enabled) {
      changes['enabled'] = enabled;
    }
    if (lowBalanceEnabled != null &&
        lowBalanceEnabled != currentState.lowBalanceEnabled) {
      changes['lowBalanceEnabled'] = lowBalanceEnabled;
    }
    if (alertThresholdEnabled != null &&
        alertThresholdEnabled != currentState.alertThresholdEnabled) {
      changes['alertThresholdEnabled'] = alertThresholdEnabled;
    }
    if (alertThreshold != null &&
        alertThreshold != currentState.alertThreshold) {
      changes['alertThreshold'] = alertThreshold;
    }
    if (daysInAdvance != null && daysInAdvance != currentState.daysInAdvance) {
      changes['daysInAdvance'] = daysInAdvance;
    }

    if (changes.isEmpty) return;

    try {
      // Update state immediately
      final newState = currentState.copyWith(
        enabled: enabled ?? currentState.enabled,
        lowBalanceEnabled: lowBalanceEnabled ?? currentState.lowBalanceEnabled,
        alertThresholdEnabled:
            alertThresholdEnabled ?? currentState.alertThresholdEnabled,
        alertThreshold: alertThreshold ?? currentState.alertThreshold,
        daysInAdvance: daysInAdvance ?? currentState.daysInAdvance,
        status: AlertStatus.checking,
      );

      // Add settings changed event
      final updatedState = newState.addEvent(
        AlertEvent.settingsChanged(changes: changes),
      );

      state = AsyncValue.data(updatedState);

      // Check and update alerts if enabled
      if (updatedState.enabled &&
          (updatedState.lowBalanceEnabled ||
              updatedState.alertThresholdEnabled)) {
        await _checkAndScheduleAlerts();
      } else {
        await _cancelAllAlerts();
      }
    } catch (e) {
      Logger.error('Error updating alert settings: $e');
      final errorState = currentState.copyWith(
        status: AlertStatus.error,
        error: AlertError.unknown(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        AlertEvent.error(errorMessage: 'Failed to update settings'),
      ));
    }
  }

  /// Check alert conditions and schedule notifications
  Future<void> _checkAndScheduleAlerts() async {
    final currentState = await future;

    try {
      state =
          AsyncValue.data(currentState.copyWith(status: AlertStatus.checking));

      // Note: AlertCoordinationService was removed - this functionality should be
      // replaced with direct dashboard provider usage or UnifiedAlertManager
      // For now, return early to prevent errors
      final errorState = currentState.copyWith(
        status: AlertStatus.error,
        error: AlertError.dashboardDataUnavailable(),
      );
      state = AsyncValue.data(errorState.addEvent(
        AlertEvent.error(errorMessage: 'Alert service temporarily unavailable'),
      ));
      return;
    } catch (e) {
      Logger.error('Error checking and scheduling alerts: $e');
      final errorState = currentState.copyWith(
        status: AlertStatus.error,
        error: AlertError.calculationError(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        AlertEvent.error(errorMessage: 'Failed to check alerts'),
      ));
    }
  }

  /// Schedule individual alert using existing coordination service logic
  Future<Map<String, dynamic>?> _scheduleAlert(
      AlertType alertType, dynamic dashboardState) async {
    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      if (alertType == AlertType.lowBalance) {
        // Check if low balance condition will be met in future
        final daysToZero = dashboardState.calculateDaysToMeterZero();
        if (daysToZero == null || daysToZero <= 1.0 || daysToZero > 7.0) {
          return null;
        }

        // Calculate when alert should trigger (24 hours before zero)
        final alertTriggerTime = DateTime.now().add(
          Duration(hours: ((daysToZero - 1.0) * 24).round()),
        );

        final notification = AppNotification(
          title: 'Low Balance Alert',
          message: 'Your meter balance will be low soon. Consider topping up.',
          timestamp: alertTriggerTime,
          type: NotificationType.lowBalance,
        );

        await notificationService.scheduleNotification(
            notification, alertTriggerTime);

        return {
          'scheduledTime': alertTriggerTime,
          'notificationId': notification.id,
        };
      } else {
        // Alert threshold logic
        final currentState = await future;
        final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
          currentState.alertThreshold,
          currentState.daysInAdvance,
        );

        if (daysToThreshold == null ||
            daysToThreshold <= 1.0 ||
            daysToThreshold > 7.0) {
          return null;
        }

        // Calculate when alert should trigger (24 hours before threshold)
        final alertTriggerTime = DateTime.now().add(
          Duration(hours: ((daysToThreshold - 1.0) * 24).round()),
        );

        final notification = AppNotification(
          title: 'Time to Top Up',
          message: 'Your balance will reach the alert threshold soon.',
          timestamp: alertTriggerTime,
          type: NotificationType.timeToTopUp,
        );

        await notificationService.scheduleNotification(
            notification, alertTriggerTime);

        return {
          'scheduledTime': alertTriggerTime,
          'notificationId': notification.id,
        };
      }
    } catch (e) {
      Logger.error('Error scheduling $alertType alert: $e');
      return null;
    }
  }

  /// Cancel all scheduled alerts
  Future<void> _cancelAllAlerts() async {
    final currentState = await future;

    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      // Cancel scheduled notifications
      if (currentState.scheduledLowBalanceId != null) {
        await notificationService
            .cancelNotification(currentState.scheduledLowBalanceId!);
      }
      if (currentState.scheduledThresholdId != null) {
        await notificationService
            .cancelNotification(currentState.scheduledThresholdId!);
      }

      final cancelledState = currentState.copyWith(
        status: AlertStatus.idle,
        nextLowBalanceAlert: null,
        nextThresholdAlert: null,
        scheduledLowBalanceId: null,
        scheduledThresholdId: null,
      );

      state = AsyncValue.data(cancelledState.addEvent(
        AlertEventExtension.cancelled(),
      ));

      Logger.info('All alerts cancelled successfully');
    } catch (e) {
      Logger.error('Error cancelling alerts: $e');
      final errorState = currentState.copyWith(
        status: AlertStatus.error,
        error: AlertError.schedulingError(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        AlertEvent.error(errorMessage: 'Failed to cancel alerts'),
      ));
    }
  }

  /// Handle alert firing (called when notification is triggered)
  Future<void> handleAlertFired(AlertType alertType) async {
    final currentState = await future;

    try {
      final firedState = currentState
          .copyWith(status: AlertStatus.fired)
          .addEvent(AlertEvent.fired(alertType: alertType));

      state = AsyncValue.data(firedState);

      // Reschedule alerts after firing
      await _checkAndScheduleAlerts();
    } catch (e) {
      Logger.error('Error handling alert firing: $e');
      final errorState = currentState.copyWith(
        status: AlertStatus.error,
        error: AlertError.unknown(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        AlertEvent.error(errorMessage: 'Failed to handle fired alert'),
      ));
    }
  }

  /// Trigger alert check (for data changes)
  Future<void> checkAlerts() async {
    final currentState = await future;

    if (!currentState.enabled) return;
    if (currentState.isProcessing) return;

    await _checkAndScheduleAlerts();
  }

  /// Clear error state
  Future<void> clearError() async {
    final currentState = await future;
    if (currentState.hasError) {
      state = AsyncValue.data(currentState.clearError());
    }
  }
}

/// Extension for AlertEvent to add cancelled factory
extension AlertEventExtension on AlertEvent {
  /// Create cancelled event
  static AlertEvent cancelled() {
    return AlertEvent(
      type: AlertEventType.cancelled,
      timestamp: DateTime.now(),
      message: 'All alerts cancelled',
      metadata: {
        'cancelled_at': DateTime.now().toIso8601String(),
      },
    );
  }
}
