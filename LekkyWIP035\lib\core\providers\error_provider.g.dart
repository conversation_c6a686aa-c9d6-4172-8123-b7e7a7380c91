// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'error_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$globalErrorHash() => r'626dd15eadb0408247ae70e34a8cc49d81a5cec9';

/// Global error handling provider
///
/// Copied from [GlobalError].
@ProviderFor(GlobalError)
final globalErrorProvider =
    AutoDisposeNotifierProvider<GlobalError, ErrorState>.internal(
  GlobalError.new,
  name: r'globalErrorProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$globalErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GlobalError = AutoDisposeNotifier<ErrorState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
