import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../constants/currency_constants.dart';

part 'localization_state.freezed.dart';
part 'localization_state.g.dart';

/// Immutable state model for localization configuration
@freezed
class LocalizationState with _$LocalizationState {
  const factory LocalizationState({
    /// Current language name
    @Default('English') String language,

    /// Current language code
    @Default('en') String languageCode,

    /// Whether the language is right-to-left
    @Default(false) bool isRTL,
  }) = _LocalizationState;

  factory LocalizationState.fromJson(Map<String, dynamic> json) =>
      _$LocalizationStateFromJson(json);
}

/// Extension methods for LocalizationState
extension LocalizationStateExtension on LocalizationState {
  /// Get display name with flag emoji
  String get displayNameWithFlag {
    switch (languageCode) {
      case 'en':
        return '🇺🇸 English';
      case 'es':
        return '🇪🇸 Spanish';
      case 'fr':
        return '🇫🇷 French';
      case 'de':
        return '🇩🇪 German';
      case 'it':
        return '🇮🇹 Italian';
      case 'pt':
        return '🇵🇹 Portuguese';
      case 'ru':
        return '🇷🇺 Russian';
      case 'zh':
        return '🇨🇳 Chinese';
      case 'ja':
        return '🇯🇵 Japanese';
      case 'hi':
        return '🇮🇳 Hindi';
      case 'ar':
        return '🇸🇦 Arabic';
      case 'he':
        return '🇮🇱 Hebrew';
      default:
        return '🇺🇸 English';
    }
  }

  /// Get flag emoji for the language
  String get flagEmoji {
    final language = RegionalConstants.getLanguage(languageCode);
    return language?.flagEmoji ?? '🇺🇸';
  }

  /// Check if language is supported
  bool get isSupported {
    return RegionalConstants.supportedLanguageCodes.contains(languageCode);
  }

  /// Get text direction based on RTL
  String get textDirection => isRTL ? 'rtl' : 'ltr';

  /// Get Locale object for Flutter
  Locale get locale => Locale(languageCode);
}
