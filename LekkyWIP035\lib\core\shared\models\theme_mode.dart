/// Enum representing different theme mode options
enum AppThemeMode {
  /// Use system theme settings
  system,
  
  /// Always use light theme
  light,
  
  /// Always use dark theme
  dark;
  
  /// Get a user-friendly display name
  String get displayName {
    switch (this) {
      case AppThemeMode.system:
        return 'System Default';
      case AppThemeMode.light:
        return 'Light Mode';
      case AppThemeMode.dark:
        return 'Dark Mode';
    }
  }
  
  /// Parse a string to get the corresponding AppThemeMode
  static AppThemeMode fromString(String value) {
    switch (value) {
      case 'system':
        return AppThemeMode.system;
      case 'light':
        return AppThemeMode.light;
      case 'dark':
        return AppThemeMode.dark;
      default:
        return AppThemeMode.system; // Default
    }
  }
  
  /// Get the string representation of the theme mode
  String get stringValue {
    switch (this) {
      case AppThemeMode.system:
        return 'system';
      case AppThemeMode.light:
        return 'light';
      case AppThemeMode.dark:
        return 'dark';
    }
  }
}
