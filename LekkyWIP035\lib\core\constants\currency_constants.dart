import 'package:flutter/material.dart';

/// Currency data model
class CurrencyData {
  final String code;
  final String symbol;
  final String name;
  final IconData icon;
  final String region;

  const CurrencyData({
    required this.code,
    required this.symbol,
    required this.name,
    required this.icon,
    required this.region,
  });
}

/// Language data model
class LanguageData {
  final String code;
  final String name;
  final String nativeName;
  final String flagEmoji;
  final bool isRTL;

  const LanguageData({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.flagEmoji,
    this.isRTL = false,
  });
}

/// Regional constants for currencies and languages
class RegionalConstants {
  /// 20 supported currencies grouped by region
  static const List<CurrencyData> currencies = [
    // North America
    CurrencyData(
      code: 'USD',
      symbol: '\$',
      name: 'US Dollar',
      icon: Icons.attach_money,
      region: 'North America',
    ),
    CurrencyData(
      code: 'CAD',
      symbol: 'C\$',
      name: 'Canadian Dollar',
      icon: Icons.attach_money,
      region: 'North America',
    ),
    CurrencyData(
      code: 'MXN',
      symbol: 'M\$',
      name: 'Mexican Peso',
      icon: Icons.currency_exchange,
      region: 'North America',
    ),

    // Europe
    CurrencyData(
      code: 'EUR',
      symbol: '€',
      name: 'Euro',
      icon: Icons.euro,
      region: 'Europe',
    ),
    CurrencyData(
      code: 'GBP',
      symbol: '£',
      name: 'British Pound',
      icon: Icons.currency_pound,
      region: 'Europe',
    ),
    CurrencyData(
      code: 'RUB',
      symbol: '₽',
      name: 'Russian Ruble',
      icon: Icons.currency_ruble,
      region: 'Europe',
    ),
    CurrencyData(
      code: 'TRY',
      symbol: '₺',
      name: 'Turkish Lira',
      icon: Icons.currency_exchange,
      region: 'Europe',
    ),

    // Asia
    CurrencyData(
      code: 'JPY',
      symbol: '¥',
      name: 'Japanese Yen',
      icon: Icons.currency_yen,
      region: 'Asia',
    ),
    CurrencyData(
      code: 'CNY',
      symbol: '¥',
      name: 'Chinese Yuan',
      icon: Icons.currency_yen,
      region: 'Asia',
    ),
    CurrencyData(
      code: 'INR',
      symbol: '₹',
      name: 'Indian Rupee',
      icon: Icons.currency_rupee,
      region: 'Asia',
    ),
    CurrencyData(
      code: 'BDT',
      symbol: '৳',
      name: 'Bangladeshi Taka',
      icon: Icons.currency_exchange,
      region: 'Asia',
    ),
    CurrencyData(
      code: 'IDR',
      symbol: 'Rp',
      name: 'Indonesian Rupiah',
      icon: Icons.currency_exchange,
      region: 'Asia',
    ),
    CurrencyData(
      code: 'PHP',
      symbol: '₱',
      name: 'Philippine Peso',
      icon: Icons.currency_exchange,
      region: 'Asia',
    ),

    // Africa
    CurrencyData(
      code: 'ZAR',
      symbol: 'R',
      name: 'South African Rand',
      icon: Icons.currency_exchange,
      region: 'Africa',
    ),
    CurrencyData(
      code: 'NGN',
      symbol: '₦',
      name: 'Nigerian Naira',
      icon: Icons.currency_exchange,
      region: 'Africa',
    ),
    CurrencyData(
      code: 'KES',
      symbol: 'KSh',
      name: 'Kenyan Shilling',
      icon: Icons.currency_exchange,
      region: 'Africa',
    ),
    CurrencyData(
      code: 'GHS',
      symbol: '₵',
      name: 'Ghanaian Cedi',
      icon: Icons.currency_exchange,
      region: 'Africa',
    ),
    CurrencyData(
      code: 'ZWL',
      symbol: 'Z\$',
      name: 'Zimbabwean Dollar',
      icon: Icons.currency_exchange,
      region: 'Africa',
    ),

    // South America & Oceania
    CurrencyData(
      code: 'BRL',
      symbol: 'R\$',
      name: 'Brazilian Real',
      icon: Icons.currency_exchange,
      region: 'South America',
    ),
    CurrencyData(
      code: 'AUD',
      symbol: 'A\$',
      name: 'Australian Dollar',
      icon: Icons.attach_money,
      region: 'Oceania',
    ),
  ];

  /// 16 strategic languages serving the most users
  static const List<LanguageData> languages = [
    // Tier 1 (High Priority - 8 languages)
    LanguageData(
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flagEmoji: '🇺🇸',
    ),
    LanguageData(
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      flagEmoji: '🇪🇸',
    ),
    LanguageData(
      code: 'pt',
      name: 'Portuguese',
      nativeName: 'Português',
      flagEmoji: '🇵🇹',
    ),
    LanguageData(
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flagEmoji: '🇫🇷',
    ),
    LanguageData(
      code: 'de',
      name: 'German',
      nativeName: 'Deutsch',
      flagEmoji: '🇩🇪',
    ),
    LanguageData(
      code: 'zh',
      name: 'Chinese',
      nativeName: '中文',
      flagEmoji: '🇨🇳',
    ),
    LanguageData(
      code: 'hi',
      name: 'Hindi',
      nativeName: 'हिन्दी',
      flagEmoji: '🇮🇳',
    ),
    LanguageData(
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flagEmoji: '🇸🇦',
      isRTL: true,
    ),

    // Tier 2 (Medium Priority - 4 languages)
    LanguageData(
      code: 'ru',
      name: 'Russian',
      nativeName: 'Русский',
      flagEmoji: '🇷🇺',
    ),
    LanguageData(
      code: 'ja',
      name: 'Japanese',
      nativeName: '日本語',
      flagEmoji: '🇯🇵',
    ),
    LanguageData(
      code: 'it',
      name: 'Italian',
      nativeName: 'Italiano',
      flagEmoji: '🇮🇹',
    ),
    LanguageData(
      code: 'id',
      name: 'Indonesian',
      nativeName: 'Bahasa Indonesia',
      flagEmoji: '🇮🇩',
    ),

    // Tier 3 (Regional Specific - 4 languages)
    LanguageData(
      code: 'tr',
      name: 'Turkish',
      nativeName: 'Türkçe',
      flagEmoji: '🇹🇷',
    ),
    LanguageData(
      code: 'bn',
      name: 'Bengali',
      nativeName: 'বাংলা',
      flagEmoji: '🇧🇩',
    ),
    LanguageData(
      code: 'sw',
      name: 'Swahili',
      nativeName: 'Kiswahili',
      flagEmoji: '🇰🇪',
    ),
    LanguageData(
      code: 'tl',
      name: 'Filipino',
      nativeName: 'Filipino',
      flagEmoji: '🇵🇭',
    ),
  ];

  /// Get currency by code
  static CurrencyData? getCurrency(String code) {
    try {
      return currencies.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }

  /// Get currency icon by code
  static IconData getCurrencyIcon(String code) {
    final currency = getCurrency(code);
    return currency?.icon ?? Icons.currency_exchange;
  }

  /// Get language by code
  static LanguageData? getLanguage(String code) {
    try {
      return languages.firstWhere((language) => language.code == code);
    } catch (e) {
      return null;
    }
  }

  /// Get currencies grouped by region
  static Map<String, List<CurrencyData>> getCurrenciesByRegion() {
    final Map<String, List<CurrencyData>> grouped = {};
    for (final currency in currencies) {
      grouped.putIfAbsent(currency.region, () => []).add(currency);
    }
    return grouped;
  }

  /// Get all supported currency codes
  static List<String> get supportedCurrencyCodes =>
      currencies.map((c) => c.code).toList();

  /// Get all supported language codes
  static List<String> get supportedLanguageCodes =>
      languages.map((l) => l.code).toList();
}
