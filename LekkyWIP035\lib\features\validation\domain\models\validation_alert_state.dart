/// Status of the validation alert system
enum ValidationAlertStatus {
  idle,
  batching,
  processing,
  fired,
  error,
}

/// Immutable state for validation alert management
class ValidationAlertState {
  /// Current validation alert status
  final ValidationAlertStatus status;
  
  /// Whether validation alerts are enabled
  final bool enabled;
  
  /// Pending invalid entries for batching
  final List<String> pendingEntries;
  
  /// Batch timeout in milliseconds
  final int batchTimeoutMs;
  
  /// When current batch started
  final DateTime? batchStartTime;
  
  /// Current error state
  final ValidationAlertError? error;
  
  /// Event history buffer (last 5 events)
  final List<ValidationAlertEvent> eventHistory;
  
  /// Last successful notification timestamp
  final DateTime? lastNotificationSent;

  const ValidationAlertState({
    this.status = ValidationAlertStatus.idle,
    this.enabled = false,
    this.pendingEntries = const [],
    this.batchTimeoutMs = 3000,
    this.batchStartTime,
    this.error,
    this.eventHistory = const [],
    this.lastNotificationSent,
  });

  /// Initial state
  factory ValidationAlertState.initial() => const ValidationAlertState();

  /// Create a copy with updated fields
  ValidationAlertState copyWith({
    ValidationAlertStatus? status,
    bool? enabled,
    List<String>? pendingEntries,
    int? batchTimeoutMs,
    DateTime? batchStartTime,
    ValidationAlertError? error,
    List<ValidationAlertEvent>? eventHistory,
    DateTime? lastNotificationSent,
  }) {
    return ValidationAlertState(
      status: status ?? this.status,
      enabled: enabled ?? this.enabled,
      pendingEntries: pendingEntries ?? this.pendingEntries,
      batchTimeoutMs: batchTimeoutMs ?? this.batchTimeoutMs,
      batchStartTime: batchStartTime ?? this.batchStartTime,
      error: error ?? this.error,
      eventHistory: eventHistory ?? this.eventHistory,
      lastNotificationSent: lastNotificationSent ?? this.lastNotificationSent,
    );
  }
}

/// Extension methods for ValidationAlertState
extension ValidationAlertStateExtension on ValidationAlertState {
  /// Check if validation alerts are active
  bool get isActive => enabled && status != ValidationAlertStatus.error;
  
  /// Check if validation alert system is in error state
  bool get hasError => error != null;
  
  /// Check if currently batching entries
  bool get isBatching => status == ValidationAlertStatus.batching && pendingEntries.isNotEmpty;
  
  /// Check if currently processing notifications
  bool get isProcessing => status == ValidationAlertStatus.processing;
  
  /// Get the most recent event
  ValidationAlertEvent? get latestEvent => 
      eventHistory.isNotEmpty ? eventHistory.last : null;
  
  /// Get user-friendly status message
  String get statusMessage {
    switch (status) {
      case ValidationAlertStatus.idle:
        return enabled ? 'Ready' : 'Disabled';
      case ValidationAlertStatus.batching:
        return 'Batching ${pendingEntries.length} invalid entries...';
      case ValidationAlertStatus.processing:
        return 'Sending notification...';
      case ValidationAlertStatus.fired:
        return 'Notification sent';
      case ValidationAlertStatus.error:
        return error?.userMessage ?? 'Error occurred';
    }
  }
  
  /// Add event to history with automatic pruning
  ValidationAlertState addEvent(ValidationAlertEvent event) {
    final newHistory = [...eventHistory, event];
    
    // Keep only last 5 events
    if (newHistory.length > 5) {
      newHistory.removeRange(0, newHistory.length - 5);
    }
    
    return copyWith(eventHistory: newHistory);
  }
  
  /// Add entry to pending batch
  ValidationAlertState addPendingEntry(String entryDetails) {
    final newPendingEntries = [...pendingEntries, entryDetails];
    final now = DateTime.now();
    
    return copyWith(
      status: ValidationAlertStatus.batching,
      pendingEntries: newPendingEntries,
      batchStartTime: batchStartTime ?? now,
    );
  }
  
  /// Clear pending entries after processing
  ValidationAlertState clearPendingEntries() {
    return copyWith(
      status: ValidationAlertStatus.idle,
      pendingEntries: [],
      batchStartTime: null,
    );
  }
  
  /// Clear error state
  ValidationAlertState clearError() {
    return copyWith(error: null);
  }
  
  /// Check if batch timeout has elapsed
  bool get isBatchTimeoutElapsed {
    if (batchStartTime == null) return false;
    final elapsed = DateTime.now().difference(batchStartTime!).inMilliseconds;
    return elapsed >= batchTimeoutMs;
  }
}

/// Validation alert error types
enum ValidationAlertErrorType {
  notificationServiceUnavailable,
  permissionDenied,
  batchProcessingError,
  validationServiceError,
  unknown,
}

/// Validation alert error severity
enum ValidationAlertErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Immutable validation alert error
class ValidationAlertError {
  final ValidationAlertErrorType type;
  final ValidationAlertErrorSeverity severity;
  final String userMessage;
  final String? technicalDetails;
  final DateTime timestamp;
  final bool shouldPersist;
  final List<String> recoveryActions;

  const ValidationAlertError({
    required this.type,
    required this.severity,
    required this.userMessage,
    this.technicalDetails,
    required this.timestamp,
    required this.shouldPersist,
    this.recoveryActions = const [],
  });

  /// Create notification service unavailable error
  factory ValidationAlertError.notificationServiceUnavailable() {
    return ValidationAlertError(
      type: ValidationAlertErrorType.notificationServiceUnavailable,
      severity: ValidationAlertErrorSeverity.high,
      userMessage: 'Notification service unavailable',
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Check notification permissions',
        'Restart the app',
        'Try again later',
      ],
    );
  }

  /// Create batch processing error
  factory ValidationAlertError.batchProcessingError({required String details}) {
    return ValidationAlertError(
      type: ValidationAlertErrorType.batchProcessingError,
      severity: ValidationAlertErrorSeverity.medium,
      userMessage: 'Error processing validation notifications',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again',
        'Check data integrity',
        'Restart the app if problem persists',
      ],
    );
  }

  /// Create unknown error
  factory ValidationAlertError.unknown({required String details}) {
    return ValidationAlertError(
      type: ValidationAlertErrorType.unknown,
      severity: ValidationAlertErrorSeverity.medium,
      userMessage: 'An unexpected error occurred',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again',
        'Restart the app if problem persists',
      ],
    );
  }

  /// Check if error can be retried
  bool get canRetry => true;
  
  /// Get primary recovery action
  String? get primaryRecoveryAction => 
      recoveryActions.isNotEmpty ? recoveryActions.first : null;
}

/// Validation alert event types
enum ValidationAlertEventType {
  entryAdded,
  batchStarted,
  notificationSent,
  batchCleared,
  error,
  settingsChanged,
}

/// Immutable validation alert event
class ValidationAlertEvent {
  final ValidationAlertEventType type;
  final DateTime timestamp;
  final String message;
  final Map<String, dynamic> metadata;

  const ValidationAlertEvent({
    required this.type,
    required this.timestamp,
    required this.message,
    this.metadata = const {},
  });

  /// Create entry added event
  factory ValidationAlertEvent.entryAdded({
    required String entryDetails,
    required int totalPending,
  }) {
    return ValidationAlertEvent(
      type: ValidationAlertEventType.entryAdded,
      timestamp: DateTime.now(),
      message: 'Invalid entry added to batch ($totalPending pending)',
      metadata: {
        'entry_details': entryDetails,
        'total_pending': totalPending,
        'added_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create batch started event
  factory ValidationAlertEvent.batchStarted({
    required int entryCount,
  }) {
    return ValidationAlertEvent(
      type: ValidationAlertEventType.batchStarted,
      timestamp: DateTime.now(),
      message: 'Started batching $entryCount invalid entries',
      metadata: {
        'entry_count': entryCount,
        'started_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create notification sent event
  factory ValidationAlertEvent.notificationSent({
    required int entryCount,
  }) {
    return ValidationAlertEvent(
      type: ValidationAlertEventType.notificationSent,
      timestamp: DateTime.now(),
      message: 'Validation notification sent for $entryCount entries',
      metadata: {
        'entry_count': entryCount,
        'sent_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create batch cleared event
  factory ValidationAlertEvent.batchCleared({
    required String reason,
  }) {
    return ValidationAlertEvent(
      type: ValidationAlertEventType.batchCleared,
      timestamp: DateTime.now(),
      message: 'Batch cleared: $reason',
      metadata: {
        'reason': reason,
        'cleared_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create error event
  factory ValidationAlertEvent.error({
    required String errorMessage,
  }) {
    return ValidationAlertEvent(
      type: ValidationAlertEventType.error,
      timestamp: DateTime.now(),
      message: 'Error: $errorMessage',
      metadata: {
        'error_at': DateTime.now().toIso8601String(),
        'error_message': errorMessage,
      },
    );
  }

  /// Create settings changed event
  factory ValidationAlertEvent.settingsChanged({
    required Map<String, dynamic> changes,
  }) {
    final changesList = changes.entries
        .map((e) => '${e.key}: ${e.value}')
        .join(', ');
    
    return ValidationAlertEvent(
      type: ValidationAlertEventType.settingsChanged,
      timestamp: DateTime.now(),
      message: 'Settings updated: $changesList',
      metadata: {
        'changed_at': DateTime.now().toIso8601String(),
        'changes': changes,
      },
    );
  }
}
