import 'dart:convert';

/// Model for preserving settings screen state during navigation
class SettingsState {
  /// Map of category index to toggle state
  final Map<int, bool> categoryToggles;
  
  /// Currently expanded category index
  final int? expandedCategoryIndex;
  
  /// Timestamp when state was saved
  final DateTime timestamp;

  /// Constructor
  const SettingsState({
    required this.categoryToggles,
    this.expandedCategoryIndex,
    required this.timestamp,
  });

  /// Create from current controller state
  factory SettingsState.fromController({
    required bool isRegionEnabled,
    required bool isNotificationsEnabled,
    required bool isDateSettingsEnabled,
    required bool isAppearanceEnabled,
    required bool isDataBackupEnabled,
    required bool isAboutEnabled,
    required bool isDonateEnabled,
    required bool isTestingEnabled,
    required int? expandedCategoryIndex,
  }) {
    return SettingsState(
      categoryToggles: {
        0: isRegionEnabled,
        1: isNotificationsEnabled,
        2: isDateSettingsEnabled,
        3: isAppearanceEnabled,
        4: isDataBackupEnabled,
        5: isAboutEnabled,
        6: isDonateEnabled,
        7: isTestingEnabled,
      },
      expandedCategoryIndex: expandedCategoryIndex,
      timestamp: DateTime.now(),
    );
  }

  /// Check if state is still valid (within 30 minutes)
  bool get isValid {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    return difference.inMinutes <= 30;
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'categoryToggles': categoryToggles.map((k, v) => MapEntry(k.toString(), v)),
      'expandedCategoryIndex': expandedCategoryIndex,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SettingsState.fromJson(Map<String, dynamic> json) {
    final categoryTogglesMap = <int, bool>{};
    final togglesJson = json['categoryToggles'] as Map<String, dynamic>;
    
    for (final entry in togglesJson.entries) {
      categoryTogglesMap[int.parse(entry.key)] = entry.value as bool;
    }

    return SettingsState(
      categoryToggles: categoryTogglesMap,
      expandedCategoryIndex: json['expandedCategoryIndex'] as int?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Convert to JSON string
  String toJsonString() => jsonEncode(toJson());

  /// Create from JSON string
  factory SettingsState.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return SettingsState.fromJson(json);
  }

  @override
  String toString() {
    return 'SettingsState(categoryToggles: $categoryToggles, '
           'expandedCategoryIndex: $expandedCategoryIndex, '
           'timestamp: $timestamp)';
  }
}
