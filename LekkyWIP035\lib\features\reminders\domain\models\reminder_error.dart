// Simplified version without freezed

/// Types of reminder errors with specific handling strategies
enum ReminderErrorType {
  permissionDenied,
  platformException,
  invalidDate,
  schedulingConflict,
  isolateError,
  serviceUnavailable,
  alarmManagerFailure,
  backgroundOptimization,
  networkError,
  unknown,
}

/// Severity levels for reminder errors
enum ReminderErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Immutable reminder error with handling strategy
class ReminderError {
  /// Type of error
  final ReminderErrorType type;

  /// Error severity
  final ReminderErrorSeverity severity;

  /// User-friendly error message
  final String userMessage;

  /// Technical error details
  final String? technicalDetails;

  /// When the error occurred
  final DateTime timestamp;

  /// Whether error should persist across app restarts
  final bool shouldPersist;

  /// Suggested recovery actions
  final List<String> recoveryActions;

  /// Additional metadata
  final Map<String, dynamic> metadata;

  const ReminderError({
    required this.type,
    required this.severity,
    required this.userMessage,
    this.technicalDetails,
    required this.timestamp,
    required this.shouldPersist,
    this.recoveryActions = const [],
    this.metadata = const {},
  });

  /// Create permission denied error
  factory ReminderError.permissionDenied({
    String? details,
  }) {
    return ReminderError(
      type: ReminderErrorType.permissionDenied,
      severity: ReminderErrorSeverity.high,
      userMessage: 'Notification permission required for reminders',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Grant notification permission in settings',
        'Enable background app refresh',
        'Disable battery optimization for this app',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': true,
      },
    );
  }

  /// Create platform exception error
  factory ReminderError.platformException({
    required String details,
  }) {
    return ReminderError(
      type: ReminderErrorType.platformException,
      severity: ReminderErrorSeverity.medium,
      userMessage: 'System error occurred while scheduling reminder',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again in a few moments',
        'Restart the app if problem persists',
        'Check device storage space',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
      },
    );
  }

  /// Create service unavailable error
  factory ReminderError.serviceUnavailable({
    String? serviceName,
  }) {
    return ReminderError(
      type: ReminderErrorType.serviceUnavailable,
      severity: ReminderErrorSeverity.high,
      userMessage: 'Reminder service temporarily unavailable',
      technicalDetails: serviceName != null ? 'Service: $serviceName' : null,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Wait a moment and try again',
        'Restart the app',
        'Check internet connection',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
        'service_name': serviceName,
      },
    );
  }

  /// Create unknown error
  factory ReminderError.unknown({
    required String details,
  }) {
    return ReminderError(
      type: ReminderErrorType.unknown,
      severity: ReminderErrorSeverity.medium,
      userMessage: 'An unexpected error occurred',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again',
        'Restart the app if problem persists',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
      },
    );
  }
}

/// Extension methods for ReminderError
extension ReminderErrorExtension on ReminderError {
  /// Create permission denied error
  static ReminderError permissionDenied({
    String? details,
  }) {
    return ReminderError(
      type: ReminderErrorType.permissionDenied,
      severity: ReminderErrorSeverity.high,
      userMessage: 'Notification permission required for reminders',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Grant notification permission in settings',
        'Enable background app refresh',
        'Disable battery optimization for this app',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': true,
      },
    );
  }

  /// Create platform exception error
  static ReminderError platformException({
    required String details,
  }) {
    return ReminderError(
      type: ReminderErrorType.platformException,
      severity: ReminderErrorSeverity.medium,
      userMessage: 'System error occurred while scheduling reminder',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again in a few moments',
        'Restart the app if problem persists',
        'Check device storage space',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
      },
    );
  }

  /// Create invalid date error
  static ReminderError invalidDate({
    required DateTime invalidDate,
  }) {
    return ReminderError(
      type: ReminderErrorType.invalidDate,
      severity: ReminderErrorSeverity.medium,
      userMessage: 'Invalid reminder date selected',
      technicalDetails: 'Date: ${invalidDate.toIso8601String()}',
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Select a future date and time',
        'Ensure date is not too far in the future',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': true,
        'invalid_date': invalidDate.toIso8601String(),
      },
    );
  }

  /// Create scheduling conflict error
  static ReminderError schedulingConflict({
    String? details,
  }) {
    return ReminderError(
      type: ReminderErrorType.schedulingConflict,
      severity: ReminderErrorSeverity.low,
      userMessage: 'Reminder scheduling conflict detected',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try a different time',
        'Cancel existing reminder first',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': true,
      },
    );
  }

  /// Create service unavailable error
  static ReminderError serviceUnavailable({
    String? serviceName,
  }) {
    return ReminderError(
      type: ReminderErrorType.serviceUnavailable,
      severity: ReminderErrorSeverity.high,
      userMessage: 'Reminder service temporarily unavailable',
      technicalDetails: serviceName != null ? 'Service: $serviceName' : null,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Wait a moment and try again',
        'Restart the app',
        'Check internet connection',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
        'service_name': serviceName,
      },
    );
  }

  /// Create alarm manager failure error (Android specific)
  static ReminderError alarmManagerFailure({
    String? details,
  }) {
    return ReminderError(
      type: ReminderErrorType.alarmManagerFailure,
      severity: ReminderErrorSeverity.high,
      userMessage: 'Device alarm system error',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: true,
      recoveryActions: [
        'Disable battery optimization for this app',
        'Enable "Allow background activity"',
        'Check device alarm settings',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': true,
        'platform_specific': 'android',
      },
    );
  }

  /// Create unknown error
  static ReminderError unknown({
    required String details,
  }) {
    return ReminderError(
      type: ReminderErrorType.unknown,
      severity: ReminderErrorSeverity.medium,
      userMessage: 'An unexpected error occurred',
      technicalDetails: details,
      timestamp: DateTime.now(),
      shouldPersist: false,
      recoveryActions: [
        'Try again',
        'Restart the app if problem persists',
      ],
      metadata: {
        'can_retry': true,
        'requires_user_action': false,
      },
    );
  }

  /// Check if error can be retried
  bool get canRetry => metadata['can_retry'] == true;

  /// Check if error requires user action
  bool get requiresUserAction => metadata['requires_user_action'] == true;

  /// Get primary recovery action
  String? get primaryRecoveryAction =>
      recoveryActions.isNotEmpty ? recoveryActions.first : null;

  /// Check if error is critical
  bool get isCritical => severity == ReminderErrorSeverity.critical;

  /// Get error age in minutes
  int get ageInMinutes => DateTime.now().difference(timestamp).inMinutes;

  /// Check if error is stale (older than 1 hour for non-persistent errors)
  bool get isStale => !shouldPersist && ageInMinutes > 60;
}
