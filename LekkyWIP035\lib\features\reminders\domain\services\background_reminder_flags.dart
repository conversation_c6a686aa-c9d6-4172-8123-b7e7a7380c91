import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';

/// Service for managing background reminder flags and communication
class BackgroundReminderFlags {
  static const String _pendingNotificationIdKey = 'pending_reminder_notification_id';
  static const String _reminderFiredKey = 'reminder_fired_flag';
  static const String _schedulingErrorKey = 'reminder_scheduling_error';
  static const String _flagTimestampKey = 'reminder_flag_timestamp';
  static const String _flagMetadataKey = 'reminder_flag_metadata';
  
  /// Maximum age for flags before automatic cleanup (7 days)
  static const int _maxFlagAgeHours = 7 * 24;

  /// Set pending notification ID flag
  static Future<void> setPendingNotificationId(int notificationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_pendingNotificationIdKey, notificationId);
      await _setFlagTimestamp();
      await _setFlagMetadata({
        'type': 'pending_notification',
        'notification_id': notificationId,
        'set_at': DateTime.now().toIso8601String(),
      });
      Logger.info('BackgroundReminderFlags: Set pending notification ID: $notificationId');
    } catch (e) {
      Logger.error('Failed to set pending notification ID: $e');
    }
  }

  /// Get pending notification ID flag
  static Future<int?> getPendingNotificationId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_pendingNotificationIdKey);
    } catch (e) {
      Logger.error('Failed to get pending notification ID: $e');
      return null;
    }
  }

  /// Clear pending notification ID flag
  static Future<void> clearPendingNotificationId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pendingNotificationIdKey);
      Logger.info('BackgroundReminderFlags: Cleared pending notification ID');
    } catch (e) {
      Logger.error('Failed to clear pending notification ID: $e');
    }
  }

  /// Set reminder fired flag
  static Future<void> setReminderFired({
    required DateTime reminderDate,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_reminderFiredKey, true);
      await _setFlagTimestamp();
      await _setFlagMetadata({
        'type': 'reminder_fired',
        'reminder_date': reminderDate.toIso8601String(),
        'fired_at': DateTime.now().toIso8601String(),
        if (metadata != null) ...metadata,
      });
      Logger.info('BackgroundReminderFlags: Set reminder fired flag for $reminderDate');
    } catch (e) {
      Logger.error('Failed to set reminder fired flag: $e');
    }
  }

  /// Check if reminder fired flag is set
  static Future<bool> isReminderFired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_reminderFiredKey) ?? false;
    } catch (e) {
      Logger.error('Failed to check reminder fired flag: $e');
      return false;
    }
  }

  /// Clear reminder fired flag
  static Future<void> clearReminderFired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_reminderFiredKey);
      Logger.info('BackgroundReminderFlags: Cleared reminder fired flag');
    } catch (e) {
      Logger.error('Failed to clear reminder fired flag: $e');
    }
  }

  /// Set scheduling error flag
  static Future<void> setSchedulingError({
    required String errorMessage,
    String? errorDetails,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorData = {
        'message': errorMessage,
        'details': errorDetails,
        'timestamp': DateTime.now().toIso8601String(),
        if (metadata != null) ...metadata,
      };
      
      await prefs.setString(_schedulingErrorKey, errorData.toString());
      await _setFlagTimestamp();
      await _setFlagMetadata({
        'type': 'scheduling_error',
        'error_message': errorMessage,
        'error_at': DateTime.now().toIso8601String(),
        if (metadata != null) ...metadata,
      });
      Logger.info('BackgroundReminderFlags: Set scheduling error flag: $errorMessage');
    } catch (e) {
      Logger.error('Failed to set scheduling error flag: $e');
    }
  }

  /// Get scheduling error flag
  static Future<String?> getSchedulingError() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_schedulingErrorKey);
    } catch (e) {
      Logger.error('Failed to get scheduling error flag: $e');
      return null;
    }
  }

  /// Clear scheduling error flag
  static Future<void> clearSchedulingError() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_schedulingErrorKey);
      Logger.info('BackgroundReminderFlags: Cleared scheduling error flag');
    } catch (e) {
      Logger.error('Failed to clear scheduling error flag: $e');
    }
  }

  /// Get all pending flags
  static Future<Map<String, dynamic>> getAllPendingFlags() async {
    final flags = <String, dynamic>{};
    
    try {
      final pendingNotificationId = await getPendingNotificationId();
      if (pendingNotificationId != null) {
        flags['pendingNotificationId'] = pendingNotificationId;
      }
      
      final reminderFired = await isReminderFired();
      if (reminderFired) {
        flags['reminderFired'] = true;
      }
      
      final schedulingError = await getSchedulingError();
      if (schedulingError != null) {
        flags['schedulingError'] = schedulingError;
      }
      
      final metadata = await _getFlagMetadata();
      if (metadata != null) {
        flags['metadata'] = metadata;
      }
      
      final timestamp = await _getFlagTimestamp();
      if (timestamp != null) {
        flags['timestamp'] = timestamp;
      }
      
    } catch (e) {
      Logger.error('Failed to get all pending flags: $e');
    }
    
    return flags;
  }

  /// Clear all flags
  static Future<void> clearAllFlags() async {
    try {
      await clearPendingNotificationId();
      await clearReminderFired();
      await clearSchedulingError();
      await _clearFlagTimestamp();
      await _clearFlagMetadata();
      Logger.info('BackgroundReminderFlags: Cleared all flags');
    } catch (e) {
      Logger.error('Failed to clear all flags: $e');
    }
  }

  /// Check if any flags are set
  static Future<bool> hasAnyFlags() async {
    try {
      final flags = await getAllPendingFlags();
      return flags.isNotEmpty;
    } catch (e) {
      Logger.error('Failed to check for flags: $e');
      return false;
    }
  }

  /// Cleanup stale flags (older than 7 days)
  static Future<void> cleanupStaleFlags() async {
    try {
      final timestamp = await _getFlagTimestamp();
      if (timestamp != null) {
        final flagAge = DateTime.now().difference(timestamp);
        if (flagAge.inHours > _maxFlagAgeHours) {
          await clearAllFlags();
          Logger.info('BackgroundReminderFlags: Cleaned up stale flags (age: ${flagAge.inHours} hours)');
        }
      }
    } catch (e) {
      Logger.error('Failed to cleanup stale flags: $e');
    }
  }

  /// Set flag timestamp
  static Future<void> _setFlagTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_flagTimestampKey, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('Failed to set flag timestamp: $e');
    }
  }

  /// Get flag timestamp
  static Future<DateTime?> _getFlagTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampString = prefs.getString(_flagTimestampKey);
      if (timestampString != null) {
        return DateTime.parse(timestampString);
      }
    } catch (e) {
      Logger.error('Failed to get flag timestamp: $e');
    }
    return null;
  }

  /// Clear flag timestamp
  static Future<void> _clearFlagTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_flagTimestampKey);
    } catch (e) {
      Logger.error('Failed to clear flag timestamp: $e');
    }
  }

  /// Set flag metadata
  static Future<void> _setFlagMetadata(Map<String, dynamic> metadata) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_flagMetadataKey, metadata.toString());
    } catch (e) {
      Logger.error('Failed to set flag metadata: $e');
    }
  }

  /// Get flag metadata
  static Future<Map<String, dynamic>?> _getFlagMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataString = prefs.getString(_flagMetadataKey);
      if (metadataString != null) {
        // Note: This is a simplified approach. In production, you'd want proper JSON serialization
        return {'raw': metadataString};
      }
    } catch (e) {
      Logger.error('Failed to get flag metadata: $e');
    }
    return null;
  }

  /// Clear flag metadata
  static Future<void> _clearFlagMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_flagMetadataKey);
    } catch (e) {
      Logger.error('Failed to clear flag metadata: $e');
    }
  }
}
