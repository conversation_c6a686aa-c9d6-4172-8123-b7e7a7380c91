import '../../../../core/models/settings_state.dart';
import '../../../../core/utils/logger.dart';

/// Service for detecting reminder-specific settings changes
class ReminderSettingsWatcher {
  /// Compare previous and current settings to detect reminder changes
  static Map<String, dynamic> detectReminderChanges(
    SettingsState? previous,
    SettingsState current,
  ) {
    final changes = <String, dynamic>{};
    
    if (previous == null) {
      // First load - return all current reminder settings
      return {
        'enabled': current.remindersEnabled,
        'frequency': current.reminderFrequency,
        'startDateTime': current.reminderStartDateTime?.toIso8601String(),
        'isInitialLoad': true,
      };
    }
    
    // Check for reminder enabled/disabled changes
    if (previous.remindersEnabled != current.remindersEnabled) {
      changes['enabled'] = current.remindersEnabled;
      Logger.info('ReminderSettingsWatcher: Reminders enabled changed to ${current.remindersEnabled}');
    }
    
    // Check for frequency changes
    if (previous.reminderFrequency != current.reminderFrequency) {
      changes['frequency'] = current.reminderFrequency;
      Logger.info('ReminderSettingsWatcher: Reminder frequency changed to ${current.reminderFrequency}');
    }
    
    // Check for start date/time changes
    if (previous.reminderStartDateTime != current.reminderStartDateTime) {
      changes['startDateTime'] = current.reminderStartDateTime?.toIso8601String();
      Logger.info('ReminderSettingsWatcher: Reminder start date/time changed to ${current.reminderStartDateTime}');
    }
    
    return changes;
  }
  
  /// Check if any reminder-related settings changed
  static bool hasReminderChanges(
    SettingsState? previous,
    SettingsState current,
  ) {
    final changes = detectReminderChanges(previous, current);
    return changes.isNotEmpty && changes['isInitialLoad'] != true;
  }
  
  /// Get user-friendly description of changes
  static String getChangesDescription(Map<String, dynamic> changes) {
    if (changes.isEmpty) return 'No changes';
    
    final descriptions = <String>[];
    
    if (changes.containsKey('enabled')) {
      final enabled = changes['enabled'] as bool;
      descriptions.add(enabled ? 'Reminders enabled' : 'Reminders disabled');
    }
    
    if (changes.containsKey('frequency')) {
      final frequency = changes['frequency'] as String;
      descriptions.add('Frequency changed to $frequency');
    }
    
    if (changes.containsKey('startDateTime')) {
      final startDateTime = changes['startDateTime'] as String?;
      if (startDateTime != null) {
        descriptions.add('Start time updated');
      } else {
        descriptions.add('Start time cleared');
      }
    }
    
    return descriptions.join(', ');
  }
  
  /// Validate reminder settings
  static List<String> validateReminderSettings(SettingsState settings) {
    final errors = <String>[];
    
    if (settings.remindersEnabled) {
      // Check if start date/time is set
      if (settings.reminderStartDateTime == null) {
        errors.add('Start date and time must be set when reminders are enabled');
      } else {
        // Check if start date/time is in the future
        if (settings.reminderStartDateTime!.isBefore(DateTime.now())) {
          errors.add('Start date and time must be in the future');
        }
        
        // Check if start date/time is not too far in the future (1 year)
        final oneYearFromNow = DateTime.now().add(const Duration(days: 365));
        if (settings.reminderStartDateTime!.isAfter(oneYearFromNow)) {
          errors.add('Start date and time cannot be more than 1 year in the future');
        }
      }
      
      // Validate frequency
      const validFrequencies = ['daily', 'weekly', 'bi-weekly', 'monthly'];
      if (!validFrequencies.contains(settings.reminderFrequency)) {
        errors.add('Invalid reminder frequency: ${settings.reminderFrequency}');
      }
    }
    
    return errors;
  }
  
  /// Check if settings are valid for reminder scheduling
  static bool areSettingsValid(SettingsState settings) {
    return validateReminderSettings(settings).isEmpty;
  }
  
  /// Get next reminder date based on current settings
  static DateTime? calculateNextReminderDate(SettingsState settings) {
    if (!settings.remindersEnabled || settings.reminderStartDateTime == null) {
      return null;
    }
    
    final now = DateTime.now();
    var nextReminder = settings.reminderStartDateTime!;
    
    // If start date is in the past, calculate next occurrence
    if (nextReminder.isBefore(now)) {
      final frequency = settings.reminderFrequency;
      
      switch (frequency) {
        case 'daily':
          while (nextReminder.isBefore(now)) {
            nextReminder = nextReminder.add(const Duration(days: 1));
          }
          break;
        case 'weekly':
          while (nextReminder.isBefore(now)) {
            nextReminder = nextReminder.add(const Duration(days: 7));
          }
          break;
        case 'bi-weekly':
          while (nextReminder.isBefore(now)) {
            nextReminder = nextReminder.add(const Duration(days: 14));
          }
          break;
        case 'monthly':
          while (nextReminder.isBefore(now)) {
            // Add one month (approximate)
            nextReminder = DateTime(
              nextReminder.year,
              nextReminder.month + 1,
              nextReminder.day,
              nextReminder.hour,
              nextReminder.minute,
            );
          }
          break;
        default:
          Logger.error('Unknown reminder frequency: $frequency');
          return null;
      }
    }
    
    return nextReminder;
  }
  
  /// Check if reminder settings require immediate action
  static bool requiresImmediateAction(Map<String, dynamic> changes) {
    // If reminders were just enabled, schedule immediately
    if (changes['enabled'] == true) {
      return true;
    }
    
    // If frequency or start time changed while enabled, reschedule
    if (changes.containsKey('frequency') || changes.containsKey('startDateTime')) {
      return true;
    }
    
    // If reminders were disabled, cancel immediately
    if (changes['enabled'] == false) {
      return true;
    }
    
    return false;
  }
  
  /// Get priority level for changes (higher number = higher priority)
  static int getChangePriority(Map<String, dynamic> changes) {
    if (changes['enabled'] == false) {
      return 3; // Highest priority - cancel reminders
    }
    
    if (changes['enabled'] == true) {
      return 2; // High priority - enable reminders
    }
    
    if (changes.containsKey('frequency') || changes.containsKey('startDateTime')) {
      return 1; // Medium priority - reschedule reminders
    }
    
    return 0; // No priority
  }
}
