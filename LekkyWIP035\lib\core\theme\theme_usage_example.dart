import 'package:flutter/material.dart';
import 'lekky_colors.dart';

/// Example widget demonstrating how to use the theme system
class ThemeUsageExample extends StatelessWidget {
  /// Constructor
  const ThemeUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Access custom Lekky colors
    final lekkyColors = Theme.of(context).lekkyColors;
    final homeAppBarColor = lekkyColors.homeAppBar;
    final textValueColor = lekkyColors.textValue;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Usage Example'),
        backgroundColor: homeAppBarColor,
      ),
      body: Container(
        color: Theme.of(context).colorScheme.background,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example of using theme text styles
            Text(
              'Heading',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Subheading',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Example of using theme buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Elevated'),
                ),
                const SizedBox(width: 8),
                OutlinedButton(
                  onPressed: () {},
                  child: const Text('Outlined'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {},
                  child: const Text('Text'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Example of using theme input fields
            const TextField(
              decoration: InputDecoration(
                labelText: 'Input Field',
                hintText: 'Enter text',
              ),
            ),
            const SizedBox(height: 16),

            // Example of using custom colors
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: lekkyColors.homeAppBar.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: lekkyColors.homeAppBar),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: lekkyColors.homeAppBar),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This is using custom Lekky colors',
                      style: TextStyle(color: textValueColor),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Example of using screen-specific colors
            Row(
              children: [
                _buildScreenColorExample(context, 'home', 'Home'),
                const SizedBox(width: 8),
                _buildScreenColorExample(context, 'history', 'History'),
                const SizedBox(width: 8),
                _buildScreenColorExample(context, 'cost', 'Cost'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScreenColorExample(
      BuildContext context, String screen, String label) {
    final color = Theme.of(context).getAppBarColor(screen);

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
