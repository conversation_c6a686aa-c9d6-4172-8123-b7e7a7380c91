import 'package:intl/intl.dart' as intl;

/// Utility class for formatting dates according to user preferences
class DateFormatter {
  /// Format a date according to user preferences
  ///
  /// If [forceShowTime] is true, time will be shown regardless of user preferences
  /// This is used for the add/edit entry dialogs
  static String formatDate(DateTime date, {bool forceShowTime = false}) {
    // Use default format for now - this can be enhanced later with async preferences
    final formattedDate = intl.DateFormat('dd-MM-yyyy').format(date);

    if (forceShowTime) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date with time for display in add/edit entry dialogs
  static String formatDateTimeForEntry(DateTime date) {
    return formatDate(date, forceShowTime: true);
  }

  /// Format a date for display in the meter reading info card
  static String formatDateForMeterInfo(DateTime date) {
    // Use default format for meter info with month name and time
    return intl.DateFormat('dd MMM yyyy, HH:mm').format(date);
  }

  /// Format a date for chart data points and axis labels
  static String formatDateForChart(DateTime date) {
    return intl.DateFormat('dd/MM').format(date);
  }

  /// Format a date for chart data points with year when needed
  static String formatDateForChartWithYear(DateTime date) {
    return intl.DateFormat('dd/MM/yyyy').format(date);
  }

  /// Format a date for validation error messages
  static String formatDateForValidation(DateTime date) {
    return intl.DateFormat('dd-MM-yyyy').format(date);
  }

  /// Format a date for dashboard components (compact format)
  static String formatDateForDashboard(DateTime date) {
    return intl.DateFormat('dd MMM').format(date);
  }

  /// Format a date for history table entries
  static String formatDateForHistory(DateTime date) {
    return formatDate(date); // Use standard format with time preference
  }

  /// Format a short date for compact displays
  static String formatDateShort(DateTime date) {
    return formatDateForChart(date); // Use chart format for consistency
  }
}
