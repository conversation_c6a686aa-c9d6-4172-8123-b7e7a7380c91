import 'cost_result.dart';
import 'cost_period.dart';
import 'cost_mode.dart';
import 'date_range.dart';
import '../../../cost/presentation/models/chart_data.dart';

/// Immutable state for the Cost Analysis feature
class CostState {
  /// Cost calculation result
  final CostResult? costResult;

  /// Selected time period
  final CostPeriod selectedPeriod;

  /// Loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Cost mode (past/future)
  final CostMode costMode;

  /// Date range for calculations
  final DateRange dateRange;

  /// Custom from date
  final DateTime? fromDate;

  /// Custom to date
  final DateTime? toDate;

  /// Date range validation error
  final String? dateRangeError;

  /// Whether there is insufficient data for custom date range
  final bool hasInsufficientData;

  /// Message to display when there is insufficient data
  final String? insufficientDataMessage;

  /// Chart data for visualization
  final List<ChartData> chartData;

  /// Recent average chart data for the third card
  final List<ChartData> recentAverageChartData;

  /// Type of average being used ("Recent Average" or "Total Average")
  final String averageType;

  /// Constructor
  CostState({
    this.costResult,
    this.selectedPeriod = CostPeriod.futureDay,
    this.isLoading = false,
    this.errorMessage,
    this.costMode = CostMode.future,
    DateRange? dateRange,
    this.fromDate,
    this.toDate,
    this.dateRangeError,
    this.hasInsufficientData = false,
    this.insufficientDataMessage,
    this.chartData = const [],
    this.recentAverageChartData = const [],
    this.averageType = "Total Average",
  }) : dateRange = dateRange ?? DateRange.now();

  /// Initial cost state
  factory CostState.initial() => CostState(isLoading: true);

  /// Create a copy with some fields changed
  CostState copyWith({
    CostResult? costResult,
    CostPeriod? selectedPeriod,
    bool? isLoading,
    String? errorMessage,
    CostMode? costMode,
    DateRange? dateRange,
    DateTime? fromDate,
    DateTime? toDate,
    String? dateRangeError,
    bool? hasInsufficientData,
    String? insufficientDataMessage,
    List<ChartData>? chartData,
    List<ChartData>? recentAverageChartData,
    String? averageType,
  }) {
    return CostState(
      costResult: costResult ?? this.costResult,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      costMode: costMode ?? this.costMode,
      dateRange: dateRange ?? this.dateRange,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      dateRangeError: dateRangeError ?? this.dateRangeError,
      hasInsufficientData: hasInsufficientData ?? this.hasInsufficientData,
      insufficientDataMessage:
          insufficientDataMessage ?? this.insufficientDataMessage,
      chartData: chartData ?? this.chartData,
      recentAverageChartData:
          recentAverageChartData ?? this.recentAverageChartData,
      averageType: averageType ?? this.averageType,
    );
  }
}

/// Extension methods for CostState
extension CostStateX on CostState {
  /// Check if there's an error
  bool get hasError => errorMessage != null;

  /// Check if there's a cost result
  bool get hasCostResult => costResult != null;

  /// Check if there's chart data
  bool get hasChartData => chartData.isNotEmpty;

  /// Check if there's recent average chart data
  bool get hasRecentAverageChartData => recentAverageChartData.isNotEmpty;

  /// Check if using recent average
  bool get isUsingRecentAverage => averageType == "Recent Average";

  /// Check if in past mode
  bool get isPastMode => costMode == CostMode.past;

  /// Check if in future mode
  bool get isFutureMode => costMode == CostMode.future;

  /// Check if using custom date range
  bool get isCustomPeriod => selectedPeriod == CostPeriod.custom;

  /// Check if date range is valid
  bool get hasValidDateRange => dateRangeError == null;

  /// Get formatted cost per period
  String get formattedCostPerPeriod {
    if (costResult == null) return '...';
    return costResult!.formattedCostPerPeriod;
  }

  /// Get formatted average usage
  String get formattedAverageUsage {
    if (costResult == null) return '...';
    return costResult!.formattedAverageUsage;
  }

  /// Get formatted net cost
  String get formattedNetCost {
    if (costResult == null) return '...';
    return costResult!.formattedNetCost;
  }

  /// Check if there was a top-up during the period
  bool get hasTopUp => costResult?.hasTopUp ?? false;

  /// Get formatted top-up amount
  String? get formattedTopUpAmount => costResult?.formattedTopUpAmount;

  /// Get chart data for display
  List<ChartData> get displayChartData {
    if (!hasChartData) return [];
    return chartData;
  }
}
