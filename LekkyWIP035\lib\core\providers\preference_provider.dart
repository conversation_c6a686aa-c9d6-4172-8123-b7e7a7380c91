import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/preference_state.dart';
import '../constants/preference_keys.dart';
import '../utils/logger.dart';
import '../../features/notifications/presentation/providers/notification_provider.dart';

part 'preference_provider.g.dart';

/// Preferences provider using AsyncNotifier for async initialization
@riverpod
class Preferences extends _$Preferences {
  @override
  Future<PreferenceState> build() async {
    return await _loadPreferences();
  }

  /// Load preferences from SharedPreferences
  Future<PreferenceState> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return PreferenceState(
        currencySymbol: prefs.getString(PreferenceKeys.currencySymbol) ?? '£',
        languageCode: prefs.getString(PreferenceKeys.language) ?? 'en',
        isSetupComplete: prefs.getBool(PreferenceKeys.setupCompleted) ?? false,
        hasShownWelcome: !(prefs.getBool(PreferenceKeys.firstLaunch) ?? true),
        alertThreshold: prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        daysInAdvance: prefs.getInt(PreferenceKeys.daysInAdvance) ?? 2,
        dateFormat: prefs.getString(PreferenceKeys.dateFormat) ?? 'dd-MM-yyyy',
        showTimeWithDate:
            prefs.getBool(PreferenceKeys.showTimeWithDate) ?? true,
        themeMode: prefs.getString(PreferenceKeys.themeMode) ?? 'system',
        notificationsEnabled:
            // Check if any notification type is enabled
            (prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false) ||
                (prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ??
                    false) ||
                (prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ??
                    false),
        lowBalanceAlertsEnabled:
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        timeToTopUpAlertsEnabled:
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        invalidRecordAlertsEnabled:
            prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false,
        initialMeterReading:
            prefs.getDouble(PreferenceKeys.initialMeterReading) ?? 0.0,
        use24HourFormat: prefs.getBool(PreferenceKeys.use24HourFormat) ?? true,
        showDecimalPlaces:
            prefs.getBool(PreferenceKeys.showDecimalPlaces) ?? true,
        decimalPlaces: prefs.getInt(PreferenceKeys.decimalPlaces) ?? 2,
      );
    } catch (e) {
      Logger.error('Failed to load preferences: $e');
      return PreferenceState.initial();
    }
  }

  /// Update currency symbol
  Future<void> setCurrencySymbol(String symbol) async {
    await _updatePreference(PreferenceKeys.currencySymbol, symbol);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(currencySymbol: symbol);
    });
  }

  /// Update language code
  Future<void> setLanguageCode(String code) async {
    await _updatePreference(PreferenceKeys.language, code);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(languageCode: code);
    });
  }

  /// Mark setup as complete
  Future<void> markSetupComplete() async {
    await _updatePreference(PreferenceKeys.setupCompleted, true);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(isSetupComplete: true);
    });

    // Show welcome notification for first-time users
    try {
      final currentState = state.value;
      if (currentState != null && !currentState.hasShownWelcome) {
        await ref
            .read(notificationProvider.notifier)
            .createWelcomeNotification();
        await markWelcomeShown();
      }
    } catch (e) {
      Logger.error('Failed to create welcome notification: $e');
      // Don't fail setup completion if notification fails
    }
  }

  /// Mark welcome as shown
  Future<void> markWelcomeShown() async {
    await _updatePreference(PreferenceKeys.firstLaunch, true);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(hasShownWelcome: true);
    });
  }

  /// Update alert threshold
  Future<void> setAlertThreshold(double threshold) async {
    await _updatePreference(PreferenceKeys.alertThreshold, threshold);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(alertThreshold: threshold);
    });
  }

  /// Update theme mode
  Future<void> setThemeMode(String mode) async {
    await _updatePreference(PreferenceKeys.themeMode, mode);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(themeMode: mode);
    });
  }

  /// Update currency (both code and symbol)
  Future<void> setCurrency(String currency, String symbol) async {
    await _updatePreference(PreferenceKeys.currency, currency);
    await _updatePreference(PreferenceKeys.currencySymbol, symbol);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(currencySymbol: symbol);
    });
  }

  /// Update language
  Future<void> setLanguage(String language) async {
    await _updatePreference(PreferenceKeys.language, language);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(languageCode: language);
    });
  }

  /// Update days in advance
  Future<void> setDaysInAdvance(int days) async {
    await _updatePreference(PreferenceKeys.daysInAdvance, days);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(daysInAdvance: days);
    });
  }

  /// Update date format
  Future<void> setDateFormat(dynamic format) async {
    String formatString;
    if (format.toString().contains('DateFormat.')) {
      // Handle DateFormat enum
      formatString = format.toString().split('.').last;
    } else {
      formatString = format.toString();
    }

    await _updatePreference(PreferenceKeys.dateFormat, formatString);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(dateFormat: formatString);
    });
  }

  /// Update show time with date
  Future<void> setShowTimeWithDate(bool show) async {
    await _updatePreference(PreferenceKeys.showTimeWithDate, show);
    state = await AsyncValue.guard(() async {
      final currentState = state.value ?? PreferenceState.initial();
      return currentState.copyWith(showTimeWithDate: show);
    });
  }

  /// Helper method to update a preference
  Future<void> _updatePreference(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (value is String) {
        await prefs.setString(key, value);
      } else if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      }
    } catch (e) {
      Logger.error('Failed to update preference $key: $e');
      rethrow;
    }
  }
}
