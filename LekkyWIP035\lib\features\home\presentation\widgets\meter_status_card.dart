import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/responsive_text_utils.dart';
import '../../../../core/utils/battery_icon_utils.dart';
import '../../../../core/providers/date_formatter_provider.dart';

/// A card that displays the current meter status
class MeterStatusCard extends ConsumerWidget {
  /// The latest meter reading value
  final double? meterValue;

  /// The date of the latest meter reading
  final DateTime? meterDate;

  /// The number of days remaining (calculated from average usage)
  final double? daysRemaining;

  /// Currency symbol to use
  final String currencySymbol;

  /// Total top-ups after latest meter reading
  final double totalTopUpsAfterLatestReading;

  /// Callback when the refresh button is pressed
  final VoidCallback onRefresh;

  /// Constructor
  const MeterStatusCard({
    super.key,
    required this.meterValue,
    required this.meterDate,
    required this.daysRemaining,
    this.currencySymbol = '₦',
    this.totalTopUpsAfterLatestReading = 0.0,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.getDashboardMainCardGradient(isDark),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Icon(
                    Icons.electric_meter,
                    color: AppColors.getDashboardMeterTextColor(isDark),
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Last Meter Reading',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: AppColors.getDashboardMeterTextColor(isDark),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: Icon(
                      Icons.refresh,
                      color: AppColors.getDashboardMeterTextColor(isDark),
                    ),
                    onPressed: onRefresh,
                    tooltip: 'Refresh dashboard data',
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildMeterValueDisplay(context),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Last Updated',
                        style: TextStyle(
                          color: AppColors.getDashboardMeterTextColor(isDark)
                              .withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        meterDate != null
                            ? ref
                                .watch(dateFormatterProvider)
                                .formatDateForDashboard(meterDate!)
                            : 'No readings yet',
                        style: TextStyle(
                          color: AppColors.getDashboardMeterTextColor(isDark),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  _buildDaysRemainingIndicator(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the meter value display with top-ups if any
  Widget _buildMeterValueDisplay(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final meterValueText = meterValue != null
        ? '$currencySymbol${meterValue!.toStringAsFixed(2)}'
        : '${currencySymbol}0.00';

    // If there are no top-ups after the latest reading, show just the meter value
    if (totalTopUpsAfterLatestReading <= 0) {
      return LayoutBuilder(
        builder: (context, constraints) {
          final responsiveFontSize =
              ResponsiveTextUtils.calculateResponsiveFontSize(
            context: context,
            text: meterValueText,
            availableWidth: constraints.maxWidth,
            baseStyle: TextStyle(
              color: AppColors.getDashboardMeterTextColor(isDark),
              fontWeight: FontWeight.bold,
            ),
          );

          return Text(
            meterValueText,
            style: TextStyle(
              color: AppColors.getDashboardMeterTextColor(isDark),
              fontSize: responsiveFontSize,
              fontWeight: FontWeight.bold,
            ),
          );
        },
      );
    }

    // Show meter value and top-ups on same line with responsive sizing
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main amounts row - meter reading and top-up on same line
        LayoutBuilder(
          builder: (context, constraints) {
            final topUpText =
                '+ $currencySymbol${totalTopUpsAfterLatestReading.toStringAsFixed(2)}';
            const spacingBetween = 16.0; // Minimum spacing between texts

            final responsiveFontSize =
                ResponsiveTextUtils.calculateDualTextFontSize(
              context: context,
              text1: meterValueText,
              text2: topUpText,
              availableWidth: constraints.maxWidth,
              spacingBetween: spacingBetween,
              baseStyle: const TextStyle(fontWeight: FontWeight.bold),
            );

            return Row(
              children: [
                Text(
                  meterValueText,
                  style: TextStyle(
                    color: AppColors.getDashboardMeterTextColor(isDark),
                    fontSize: responsiveFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  topUpText,
                  style: TextStyle(
                    color: AppColors.getDashboardTopUpTextColor(isDark),
                    fontSize: responsiveFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 2),
        // "Total Top-ups" text positioned under the top-up amount, right-aligned
        Row(
          children: [
            const Spacer(),
            Text(
              'Total Top-ups',
              style: TextStyle(
                color: AppColors.getDashboardTopUpTextColor(isDark)
                    .withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the days remaining indicator
  Widget _buildDaysRemainingIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (daysRemaining == null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.getDashboardMeterTextColor(isDark).withOpacity(0.2),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          'N/A',
          style: TextStyle(
            color: AppColors.getDashboardMeterTextColor(isDark),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // Determine color based on days remaining using theme-aware colors
    Color indicatorColor;
    if (daysRemaining! <= 2) {
      indicatorColor = AppColors.getErrorIndicatorColor(isDark);
    } else if (daysRemaining! <= 5) {
      indicatorColor = AppColors.getWarningIndicatorColor(isDark);
    } else {
      indicatorColor = AppColors.getAddButtonColor(isDark);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: indicatorColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            BatteryIconUtils.getBatteryIconForDays(daysRemaining),
            color: AppColors.getDashboardMeterTextColor(isDark),
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${daysRemaining!.toStringAsFixed(1)} days',
            style: TextStyle(
              color: AppColors.getDashboardMeterTextColor(isDark),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
