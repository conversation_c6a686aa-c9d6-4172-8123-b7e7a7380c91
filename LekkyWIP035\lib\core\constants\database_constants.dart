/// Constants for database operations
class DatabaseConstants {
  /// Database name
  static const String databaseName = 'lekky.db';

  /// Database version
  static const int databaseVersion = 7;

  /// Meter readings table name
  static const String meterReadingsTable = 'meter_readings';

  /// Top-ups table name
  static const String topUpsTable = 'top_ups';

  /// Averages table name
  static const String averagesTable = 'averages';

  /// Per-reading averages table name
  static const String perReadingAveragesTable = 'per_reading_averages';

  /// Settings table name
  static const String settingsTable = 'settings';

  /// Version table name
  static const String versionTable = 'version';

  /// Notifications table name
  static const String notificationsTable = 'notifications';

  /// Maximum number of meter readings to store
  static const int maxMeterReadings = 630;

  /// Maximum number of top-ups to store
  static const int maxTopUps = 630;

  /// Number of entries per page for pagination
  static const int entriesPerPage = 50;

  /// Performance target for read operations (milliseconds)
  static const int readPerformanceTarget = 100;

  /// Performance target for write operations (milliseconds)
  static const int writePerformanceTarget = 50;

  /// Threshold for archiving data (number of entries)
  static const int archiveThreshold = 1000;
}
