import 'package:flutter/material.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/date_formatter.dart';

/// Import strategy options
enum ImportStrategy {
  /// Replace all existing data with imported data
  replace,

  /// Merge imported data with existing data
  merge,

  /// Cancel the import
  cancel,
}

/// Dialog for selecting import strategy
class ImportStrategyDialog extends StatelessWidget {
  /// List of entries to be imported
  final List<MeterEntry> entries;

  /// Constructor
  const ImportStrategyDialog({
    super.key,
    required this.entries,
  });

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    // Debug: Print the calculated width
    debugPrint(
        'ImportStrategyDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  @override
  Widget build(BuildContext context) {
    final meterReadings = entries.where((e) => e.isReading).length;
    final topUps = entries.where((e) => e.isTopUp).length;
    final dateRange = _getDateRange();

    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 32, // Reduced from 40 to increase height by ~7%
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dialog header
            Row(
              children: [
                Icon(
                  Icons.file_download,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Import Data',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () =>
                      Navigator.of(context).pop(ImportStrategy.cancel),
                  tooltip: 'Close',
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Content
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Import Summary:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text('• ${entries.length} total entries'),
                Text('• $meterReadings meter readings'),
                Text('• $topUps top-ups'),
                if (dateRange.isNotEmpty) Text('• Date range: $dateRange'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.warning,
                              color: Colors.orange, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Choose Import Strategy:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• Replace: Delete all existing data and import new data\n'
                        '• Merge: Add new data to existing data (duplicates will be flagged)',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Button bar
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: LekkyButton(
                    text: 'Cancel',
                    type: LekkyButtonType.secondary,
                    size: LekkyButtonSize.compact,
                    onPressed: () =>
                        Navigator.of(context).pop(ImportStrategy.cancel),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LekkyButton(
                    text: 'Merge',
                    type: LekkyButtonType.special,
                    size: LekkyButtonSize.compact,
                    onPressed: () =>
                        Navigator.of(context).pop(ImportStrategy.merge),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LekkyButton(
                    text: 'Replace',
                    type: LekkyButtonType.destructive,
                    size: LekkyButtonSize.compact,
                    onPressed: () =>
                        Navigator.of(context).pop(ImportStrategy.replace),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get formatted date range string
  String _getDateRange() {
    if (entries.isEmpty) return '';

    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));

    final firstDate = sortedEntries.first.date;
    final lastDate = sortedEntries.last.date;

    if (firstDate.isAtSameMomentAs(lastDate)) {
      return DateFormatter.formatDate(firstDate);
    }

    return '${DateFormatter.formatDate(firstDate)} - ${DateFormatter.formatDate(lastDate)}';
  }
}
