// File: lib/features/splash/presentation/screens/simplified_splash_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/preference_provider.dart';
import '../widgets/simplified_splash_animation.dart';

/// The splash screen of the app
class SimplifiedSplashScreen extends ConsumerStatefulWidget {
  const SimplifiedSplashScreen({super.key});

  @override
  ConsumerState<SimplifiedSplashScreen> createState() =>
      _SimplifiedSplashScreenState();
}

class _SimplifiedSplashScreenState
    extends ConsumerState<SimplifiedSplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeAndNavigate();
  }

  Future<void> _initializeAndNavigate() async {
    // Simulate a delay for the splash screen
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    try {
      // Wait for fresh preferences data from SharedPreferences
      final preferences = await ref.read(preferencesProvider.future);

      if (!mounted) return;

      if (preferences.isSetupComplete) {
        Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
      } else {
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    } catch (e) {
      // Default to welcome screen if error loading preferences
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFF003087), // Deep blue to match welcome screen
        ),
        child: const Center(
          child: SimplifiedSplashAnimation(),
        ),
      ),
    );
  }
}
