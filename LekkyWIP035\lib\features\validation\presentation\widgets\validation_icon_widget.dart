import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/validation_provider.dart';

/// A reusable validation icon widget that shows validation status
///
/// Features:
/// - White icon when all entries are valid
/// - Red icon when invalid entries exist
/// - Loading indicator during validation
/// - Error state with inverted colors for validation failures
class ValidationIconWidget extends ConsumerWidget {
  /// Callback when the icon is tapped
  final VoidCallback onTap;

  /// Constructor
  const ValidationIconWidget({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationState = ref.watch(validationControllerProvider);

    return _buildIcon(context, validationState);
  }

  /// Build the appropriate icon based on validation state
  Widget _buildIcon(BuildContext context, ValidationState state) {
    // Handle loading state with loading indicator
    if (state.isLoading) {
      return Stack(
        children: [
          // Base icon (dimmed)
          IconButton(
            icon: Icon(
              Icons.check_circle,
              color: Colors.white.withOpacity(0.5),
              size: 28,
            ),
            onPressed: onTap,
            tooltip: 'Entry Validation (Loading...)',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          // Loading indicator overlay
          const Positioned(
            right: 0,
            top: 0,
            child: SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        ],
      );
    }

    // Handle error state with inverted colors
    if (state.errorMessage != null) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.black, width: 2),
        ),
        child: IconButton(
          icon: const Icon(
            Icons.error_outline,
            color: Colors.black,
            size: 24,
          ),
          onPressed: onTap,
          tooltip: 'Entry Validation (Error)',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      );
    }

    // Determine icon color based on validation results
    final iconColor = _getIconColor(state);

    return IconButton(
      icon: Icon(
        Icons.check_circle,
        color: iconColor,
        size: 28,
      ),
      onPressed: onTap,
      tooltip: 'Entry Validation',
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
    );
  }

  /// Get icon color based on validation state
  Color _getIconColor(ValidationState state) {
    // Check if there are invalid entries
    if (state.integrityReport != null &&
        state.integrityReport!.invalidEntriesCount > 0) {
      return Colors.red;
    }

    // Default to white for valid entries or no data
    return Colors.white;
  }
}
